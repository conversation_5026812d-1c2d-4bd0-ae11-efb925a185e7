package task

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/facefusion"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
)

// TestFaceFusionIntegration 测试人脸融合任务与队列的整合
func TestFaceFusionIntegration(t *testing.T) {
	// 创建测试请求
	testReq := &bean.FaceFusionTaskRequest{
		TaskID:  "test-task-" + time.Now().Format("20060102150405"),
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{
				"Url": "https://example.com/test-image.jpg",
			},
		},
		RspImgType: "base64",
	}

	// 测试队列统计信息
	queue := facefusion.GetGlobalQueue()
	initialStats := queue.GetStats()
	t.Logf("Initial queue stats: %+v", initialStats)

	// 测试参数验证（这个不依赖外部服务）
	t.Run("ParameterValidation", func(t *testing.T) {
		err := validateFaceFusionTaskRequestNew(testReq)
		assert.NoError(t, err, "Valid request should pass validation")

		// 测试无效请求
		invalidReq := &bean.FaceFusionTaskRequest{}
		err = validateFaceFusionTaskRequestNew(invalidReq)
		assert.Error(t, err, "Invalid request should fail validation")

		t.Logf("Validation test passed")
	})

	// 测试通过 asynq 任务处理（仅测试解析部分）
	t.Run("AsynqTaskUnmarshaling", func(t *testing.T) {
		// 序列化请求
		payload, err := json.Marshal(testReq)
		assert.NoError(t, err)

		// 创建 asynq 任务
		task := asynq.NewTask(TypeFaceFusion, payload)

		// 测试解析
		var parsedReq bean.FaceFusionTaskRequest
		err = json.Unmarshal(task.Payload(), &parsedReq)
		assert.NoError(t, err)

		assert.Equal(t, testReq.TaskID, parsedReq.TaskID)
		assert.Equal(t, testReq.GameID, parsedReq.GameID)
		assert.Equal(t, testReq.UserID, parsedReq.UserID)
		assert.Equal(t, testReq.ModelID, parsedReq.ModelID)

		t.Logf("Asynq task unmarshaling test passed")
	})

	// 测试队列统计信息变化
	finalStats := queue.GetStats()
	t.Logf("Final queue stats: %+v", finalStats)
}

// TestQueueConfiguration 测试队列配置
func TestQueueConfiguration(t *testing.T) {
	queue := facefusion.GetGlobalQueue()
	stats := queue.GetStats()

	// 验证队列配置
	assert.NotNil(t, queue)
	assert.GreaterOrEqual(t, stats.QPSLimit, 1)
	assert.GreaterOrEqual(t, stats.MaxQueueSize, 0)

	t.Logf("Queue configuration: QPS=%d, MaxSize=%d, Enabled=%v",
		stats.QPSLimit, stats.MaxQueueSize, stats.EnableQPSLimit)
}

// BenchmarkFaceFusionProcessing 性能测试
func BenchmarkFaceFusionProcessing(b *testing.B) {
	ctx := context.Background()

	testReq := &bean.FaceFusionTaskRequest{
		TaskID:  "bench-task",
		GameID:  "bench-game",
		UserID:  "bench-user",
		ModelID: "bench-model",
		MergeInfos: []map[string]string{
			{
				"Url": "https://example.com/bench-image.jpg",
			},
		},
		RspImgType: "base64",
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 创建新的任务ID避免冲突
		testReq.TaskID = "bench-task-" + string(rune(i))

		_, _ = processFaceFusionTaskDirectly(ctx, testReq)
	}
}
