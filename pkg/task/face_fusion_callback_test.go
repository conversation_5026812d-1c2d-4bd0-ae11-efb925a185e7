package task

import (
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/encryption"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestFaceFusionCallbackWithSecureURL 测试人脸融合回调中的安全URL生成
func TestFaceFusionCallbackWithSecureURL(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret
	originalBaseURL := config.GlobConfig.Server.BaseURL
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption
	originalSSECKey := config.GlobConfig.OSS.SSECEncryptionKey

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
		config.GlobConfig.Server.BaseURL = originalBaseURL
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
		config.GlobConfig.OSS.SSECEncryptionKey = originalSSECKey
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-callback"
	config.GlobConfig.Server.BaseURL = "https://test-api-callback.example.com"
	config.GlobConfig.OSS.EnableSSEC = true
	config.GlobConfig.OSS.FaceFusionEncryption = true

	// 生成测试密钥
	testKey, err := encryption.GenerateSSECKey()
	assert.NoError(t, err)
	config.GlobConfig.OSS.SSECEncryptionKey = testKey

	// 测试成功状态的结果（模拟已上传到OSS的情况）
	successResult := &bean.FaceFusionTaskResult{
		TaskID:      "test-callback-task-success",
		GameID:      "test-callback-game",
		UserID:      "test-callback-user",
		ModelID:     "test-callback-model",
		ProjectID:   "test-project-callback",
		FusedImage:  "", // 空字符串表示已上传到OSS
		RequestID:   "test-request-callback",
		Status:      "success",
		Message:     "Face fusion completed successfully",
		ProcessedAt: time.Now(),
	}

	// 模拟回调前的安全URL生成逻辑
	if successResult.Status == "success" && successResult.FusedImage == "" {
		// 这里模拟任务处理中的逻辑
		t.Logf("模拟生成安全URL: task_id=%s", successResult.TaskID)

		// 由于没有实际的数据库记录，这里只测试配置是否正确
		enabled := encryption.IsFaceFusionEncryptionEnabled()
		assert.True(t, enabled, "加密功能应该启用")

		// 模拟生成的安全URL（实际情况下会调用imageService.GenerateSecureImageURL）
		mockSecureURL := "https://test-api-callback.example.com/api/face_fusion/image/test-callback-task-success?token=mock-8day-token"
		successResult.FusedImage = mockSecureURL

		t.Logf("模拟生成8天有效期安全URL: %s", mockSecureURL)
	}

	// 验证结果
	assert.Equal(t, "success", successResult.Status)
	assert.NotEmpty(t, successResult.FusedImage)
	assert.Contains(t, successResult.FusedImage, "token=")

	t.Logf("成功状态测试完成: FusedImage=%s", successResult.FusedImage)
}

// TestFaceFusionCallbackWithoutEncryption 测试未启用加密时的回调
func TestFaceFusionCallbackWithoutEncryption(t *testing.T) {
	// 备份原配置
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption

	defer func() {
		// 恢复原配置
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
	}()

	// 设置未启用加密的配置
	config.GlobConfig.OSS.EnableSSEC = false
	config.GlobConfig.OSS.FaceFusionEncryption = false

	// 测试成功状态的结果
	successResult := &bean.FaceFusionTaskResult{
		TaskID:      "test-callback-task-no-encryption",
		GameID:      "test-callback-game",
		UserID:      "test-callback-user",
		ModelID:     "test-callback-model",
		ProjectID:   "test-project-callback",
		FusedImage:  "", // 空字符串表示已上传到OSS
		RequestID:   "test-request-callback",
		Status:      "success",
		Message:     "Face fusion completed successfully",
		ProcessedAt: time.Now(),
	}

	// 模拟回调前的逻辑（未启用加密时不应该生成安全URL）
	if successResult.Status == "success" && successResult.FusedImage == "" {
		enabled := encryption.IsFaceFusionEncryptionEnabled()
		assert.False(t, enabled, "加密功能应该未启用")

		// 未启用加密时，FusedImage应该保持为空或使用原有的公开URL
		t.Logf("未启用加密，跳过安全URL生成")
	}

	// 验证结果
	assert.Equal(t, "success", successResult.Status)
	// 未启用加密时，FusedImage应该为空（或者是原有的公开URL）

	t.Logf("未启用加密测试完成: FusedImage=%s", successResult.FusedImage)
}

// TestFaceFusionCallbackFailedStatus 测试失败状态的回调
func TestFaceFusionCallbackFailedStatus(t *testing.T) {
	// 设置测试配置
	config.GlobConfig.OSS.EnableSSEC = true
	config.GlobConfig.OSS.FaceFusionEncryption = true

	// 测试失败状态的结果
	failedResult := &bean.FaceFusionTaskResult{
		TaskID:      "test-callback-task-failed",
		GameID:      "test-callback-game",
		UserID:      "test-callback-user",
		ModelID:     "test-callback-model",
		ProjectID:   "test-project-callback",
		FusedImage:  "",
		RequestID:   "test-request-callback",
		Status:      "failed",
		Message:     "Face fusion failed due to invalid input",
		ProcessedAt: time.Now(),
	}

	// 模拟回调前的逻辑（失败状态不应该生成安全URL）
	if failedResult.Status == "success" && failedResult.FusedImage == "" {
		t.Error("失败状态不应该进入安全URL生成逻辑")
	}

	// 验证结果
	assert.Equal(t, "failed", failedResult.Status)
	assert.Empty(t, failedResult.FusedImage) // 失败状态应该没有图片URL

	t.Logf("失败状态测试完成: Status=%s, Message=%s", failedResult.Status, failedResult.Message)
}

// TestSecureURLExpiration 测试8天有效期的配置
func TestSecureURLExpiration(t *testing.T) {
	// 8天 = 8 * 24 * 60 = 11520分钟
	expectedMinutes := 8 * 24 * 60
	assert.Equal(t, 11520, expectedMinutes, "8天应该等于11520分钟")

	t.Logf("8天有效期配置验证: %d分钟", expectedMinutes)
}
