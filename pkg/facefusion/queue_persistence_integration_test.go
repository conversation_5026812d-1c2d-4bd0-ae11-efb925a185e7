package facefusion

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

func TestQueuePersistenceIntegration(t *testing.T) {
	// 跳过测试如果没有 Redis 连接
	if !isRedisAvailable() {
		t.Skip("Redis not available, skipping persistence integration tests")
	}

	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	// 创建队列
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)
	assert.NotNil(t, queue.persistence)
	assert.True(t, queue.persistence.IsEnabled())

	// 确保测试开始前清理
	_ = queue.persistence.ClearTasks()

	// 提交一些任务到队列
	ctx := context.Background()
	taskIDs := []string{"task-1", "task-2", "task-3"}
	
	for _, taskID := range taskIDs {
		go func(id string) {
			request := &bean.FaceFusionTaskRequest{
				TaskID:  id,
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
			}
			_, err := queue.Submit(ctx, request)
			if err != nil {
				t.Logf("Task %s submission failed: %v", id, err)
			}
		}(taskID)
	}

	// 等待任务进入队列
	time.Sleep(100 * time.Millisecond)

	// 验证队列中有任务
	stats := queue.GetStats()
	assert.Greater(t, stats.QueueSize, 0, "Queue should have tasks")

	// 模拟应用关闭 - 这会触发持久化
	queue.Shutdown()

	// 验证任务已保存到 Redis
	persistentTasks, err := queue.persistence.LoadTasks()
	assert.NoError(t, err)
	t.Logf("Found %d persistent tasks after shutdown", len(persistentTasks))

	// 创建新的队列实例（模拟应用重启）
	newQueue := NewQPSLimitedQueue()
	assert.NotNil(t, newQueue)

	// 验证任务已从持久化存储恢复
	newStats := newQueue.GetStats()
	t.Logf("New queue stats after restoration: %+v", newStats)

	// 清理
	newQueue.Shutdown()
	_ = newQueue.persistence.ClearTasks()
}

func TestQueuePersistenceDisabledIntegration(t *testing.T) {
	// 设置测试配置 - 禁用QPS限制，这也会禁用持久化
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      false, // 禁用QPS限制
		},
	}

	// 创建队列
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)
	
	// 验证持久化被禁用
	if queue.persistence != nil {
		assert.False(t, queue.persistence.IsEnabled())
	}

	// 提交任务应该直接处理，不经过队列
	ctx := context.Background()
	request := &bean.FaceFusionTaskRequest{
		TaskID:  "direct-task",
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
	}

	result, err := queue.Submit(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "direct-task", result.TaskID)

	// 验证队列为空（因为任务直接处理）
	stats := queue.GetStats()
	assert.Equal(t, 0, stats.QueueSize)

	// 关闭队列
	queue.Shutdown()
}

func TestQueuePersistenceTaskExpiration(t *testing.T) {
	// 跳过测试如果没有 Redis 连接
	if !isRedisAvailable() {
		t.Skip("Redis not available, skipping persistence integration tests")
	}

	// 设置测试配置 - 使用很短的超时时间
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			ProjectID:           "test-project",
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 1, // 1秒超时
			EnableQPSLimit:      true,
		},
	}

	// 创建持久化管理器并保存一个过期任务
	redisKey := "test:facefusion:queue:test-project"
	persistence := NewQueuePersistence(redisKey, true)
	assert.True(t, persistence.IsEnabled())

	// 清理
	_ = persistence.ClearTasks()

	// 创建一个过期的任务
	expiredTask := &QueuedTask{
		Request: &bean.FaceFusionTaskRequest{
			TaskID:  "expired-task",
			GameID:  "test-game",
			UserID:  "test-user",
			ModelID: "test-model",
		},
		SubmittedAt: time.Now().Add(-2 * time.Second), // 2秒前提交，已过期
		ResultChan:  make(chan *TaskResult, 1),
		Context:     context.Background(),
	}

	// 保存过期任务
	err := persistence.SaveTasks([]*QueuedTask{expiredTask})
	assert.NoError(t, err)

	// 创建新队列（模拟重启）
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)

	// 验证过期任务没有被恢复
	stats := queue.GetStats()
	assert.Equal(t, 0, stats.QueueSize, "Expired tasks should not be restored")

	// 验证持久化数据已被清除
	persistentTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, persistentTasks, 0, "Expired tasks should be cleared from persistence")

	// 清理
	queue.Shutdown()
	_ = persistence.ClearTasks()
}
