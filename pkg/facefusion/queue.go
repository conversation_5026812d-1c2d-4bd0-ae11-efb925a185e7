package facefusion

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// QPSLimitedQueue QPS限制队列
type QPSLimitedQueue struct {
	// 配置
	qpsLimit            int  // QPS限制
	maxQueueSize        int  // 最大队列大小
	queueTimeoutSeconds int  // 队列超时时间
	enableQPSLimit      bool // 是否启用QPS限制

	// 队列状态
	queue           chan *QueuedTask // 任务队列
	activeRequests  int              // 当前活跃请求数
	lastRequestTime time.Time        // 上次请求时间
	mutex           sync.RWMutex     // 读写锁

	// 生命周期管理
	ctx        context.Context    // 队列上下文
	cancel     context.CancelFunc // 取消函数
	shutdownWG sync.WaitGroup     // 等待组，确保goroutine正常退出

	// 持久化
	persistence *QueuePersistence // 持久化管理器

	// 任务处理器
	processor TaskProcessor // 任务处理器函数

	// 统计信息
	totalProcessed int64 // 总处理数量
	totalQueued    int64 // 总排队数量
	totalTimeout   int64 // 总超时数量
	totalRejected  int64 // 总拒绝数量
}

// QueuedTask 队列中的任务
type QueuedTask struct {
	Request     *bean.FaceFusionTaskRequest // 任务请求
	SubmittedAt time.Time                   // 提交时间
	ResultChan  chan *TaskResult            // 结果通道
	Context     context.Context             // 上下文
}

// TaskResult 任务结果
type TaskResult struct {
	Result *bean.FaceFusionTaskResult // 处理结果
	Error  error                      // 错误信息
}

// TaskProcessor 任务处理器函数类型
type TaskProcessor func(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error)

// QueueStats 队列统计信息
type QueueStats struct {
	QueueSize      int   `json:"queue_size"`       // 当前队列大小
	ActiveRequests int   `json:"active_requests"`  // 当前活跃请求数
	TotalProcessed int64 `json:"total_processed"`  // 总处理数量
	TotalQueued    int64 `json:"total_queued"`     // 总排队数量
	TotalTimeout   int64 `json:"total_timeout"`    // 总超时数量
	TotalRejected  int64 `json:"total_rejected"`   // 总拒绝数量
	QPSLimit       int   `json:"qps_limit"`        // QPS限制
	MaxQueueSize   int   `json:"max_queue_size"`   // 最大队列大小
	EnableQPSLimit bool  `json:"enable_qps_limit"` // 是否启用QPS限制
}

var (
	globalQueue *QPSLimitedQueue
	queueOnce   sync.Once
)

// GetGlobalQueue 获取全局队列实例
func GetGlobalQueue() *QPSLimitedQueue {
	queueOnce.Do(func() {
		globalQueue = NewQPSLimitedQueue()
	})
	return globalQueue
}

// NewQPSLimitedQueue 创建新的QPS限制队列
func NewQPSLimitedQueue() *QPSLimitedQueue {
	conf := config.GlobConfig.FaceFusion

	// 设置默认值
	qpsLimit := conf.QPSLimit
	if qpsLimit <= 0 {
		qpsLimit = 18 // 默认18 QPS，为腾讯云20 QPS限制留安全余量
	}

	maxQueueSize := conf.MaxQueueSize
	if maxQueueSize < 0 {
		maxQueueSize = 1000 // 默认最大队列1000
	}
	// 允许maxQueueSize为0用于测试

	queueTimeoutSeconds := conf.QueueTimeoutSeconds
	if queueTimeoutSeconds <= 0 {
		queueTimeoutSeconds = 300 // 默认5分钟超时
	}

	enableQPSLimit := conf.EnableQPSLimit // 默认为false，需要显式配置

	// 创建上下文用于生命周期管理
	ctx, cancel := context.WithCancel(context.Background())

	// 初始化持久化管理器
	redisKey := fmt.Sprintf("facefusion:queue:%s", conf.ProjectID)
	persistence := NewQueuePersistence(redisKey, enableQPSLimit) // 只有启用QPS限制时才启用持久化

	queue := &QPSLimitedQueue{
		qpsLimit:            qpsLimit,
		maxQueueSize:        maxQueueSize,
		queueTimeoutSeconds: queueTimeoutSeconds,
		enableQPSLimit:      enableQPSLimit,
		queue:               make(chan *QueuedTask, maxQueueSize),
		lastRequestTime:     time.Now(),
		ctx:                 ctx,
		cancel:              cancel,
		persistence:         persistence,
	}

	// 从持久化存储恢复任务
	queue.restoreTasksFromPersistence()

	// 启动处理协程
	queue.shutdownWG.Add(1)
	go queue.processQueue()

	// 安全地记录日志（处理测试环境中logger可能为nil的情况）
	if logger.Logger != nil {
		logger.Logger.Infof("FaceFusion QPS queue initialized: qps_limit=%d, max_queue_size=%d, timeout=%ds, enabled=%v",
			qpsLimit, maxQueueSize, queueTimeoutSeconds, enableQPSLimit)
	}

	return queue
}

// Submit 提交任务到队列
func (q *QPSLimitedQueue) Submit(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
	// 如果未启用QPS限制，直接处理
	if !q.enableQPSLimit {
		return q.processTaskDirectly(ctx, request)
	}

	// 检查队列是否已满
	if len(q.queue) >= q.maxQueueSize {
		q.mutex.Lock()
		q.totalRejected++
		q.mutex.Unlock()
		return nil, fmt.Errorf("queue is full, current size: %d, max size: %d", len(q.queue), q.maxQueueSize)
	}

	// 创建队列任务
	queuedTask := &QueuedTask{
		Request:     request,
		SubmittedAt: time.Now(),
		ResultChan:  make(chan *TaskResult, 1),
		Context:     ctx,
	}

	// 提交到队列
	select {
	case q.queue <- queuedTask:
		q.mutex.Lock()
		q.totalQueued++
		q.mutex.Unlock()
		if logger.Logger != nil {
			logger.Logger.InfofCtx(ctx, "FaceFusion task queued: task_id=%s, queue_size=%d", request.TaskID, len(q.queue))
		}
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 等待结果
	timeout := time.Duration(q.queueTimeoutSeconds) * time.Second
	select {
	case result := <-queuedTask.ResultChan:
		if result.Error != nil {
			return nil, result.Error
		}
		return result.Result, nil
	case <-time.After(timeout):
		q.mutex.Lock()
		q.totalTimeout++
		q.mutex.Unlock()
		return nil, fmt.Errorf("task timeout after %v: task_id=%s", timeout, request.TaskID)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// SubmitWithProcessor 使用自定义处理器提交任务到队列
func (q *QPSLimitedQueue) SubmitWithProcessor(ctx context.Context, request *bean.FaceFusionTaskRequest, processor TaskProcessor) (*bean.FaceFusionTaskResult, error) {
	// 如果未启用QPS限制，直接处理
	if !q.enableQPSLimit {
		return processor(ctx, request)
	}

	// 检查队列是否已满
	if len(q.queue) >= q.maxQueueSize {
		q.mutex.Lock()
		q.totalRejected++
		q.mutex.Unlock()
		return nil, fmt.Errorf("queue is full, current size: %d, max size: %d", len(q.queue), q.maxQueueSize)
	}

	// 临时设置处理器
	originalProcessor := q.processor
	q.processor = processor
	defer func() {
		q.processor = originalProcessor
	}()

	// 创建队列任务
	queuedTask := &QueuedTask{
		Request:     request,
		SubmittedAt: time.Now(),
		ResultChan:  make(chan *TaskResult, 1),
		Context:     ctx,
	}

	// 提交到队列
	select {
	case q.queue <- queuedTask:
		q.mutex.Lock()
		q.totalQueued++
		q.mutex.Unlock()
		if logger.Logger != nil {
			logger.Logger.InfofCtx(ctx, "FaceFusion task queued: task_id=%s, queue_size=%d", request.TaskID, len(q.queue))
		}
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 等待结果
	timeout := time.Duration(q.queueTimeoutSeconds) * time.Second
	select {
	case result := <-queuedTask.ResultChan:
		if result.Error != nil {
			return nil, result.Error
		}
		return result.Result, nil
	case <-time.After(timeout):
		q.mutex.Lock()
		q.totalTimeout++
		q.mutex.Unlock()
		return nil, fmt.Errorf("task timeout after %v: task_id=%s", timeout, request.TaskID)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// processQueue 处理队列中的任务
func (q *QPSLimitedQueue) processQueue() {
	defer q.shutdownWG.Done() // 确保在退出时通知等待组

	ticker := time.NewTicker(time.Second / time.Duration(q.qpsLimit)) // 根据QPS计算间隔
	defer ticker.Stop()

	for {
		select {
		case task, ok := <-q.queue:
			if !ok {
				// 队列已关闭，退出处理循环
				if logger.Logger != nil {
					logger.Logger.Info("FaceFusion queue closed, processQueue exiting")
				}
				return
			}

			// 检查是否需要退出
			select {
			case <-q.ctx.Done():
				// 上下文已取消，将任务放回队列或直接处理错误
				task.ResultChan <- &TaskResult{
					Error: fmt.Errorf("queue is shutting down"),
				}
				return
			default:
				// 继续处理
			}

			// 等待QPS限制
			select {
			case <-ticker.C:
				// 异步处理任务
				go q.handleQueuedTask(task)
			case <-q.ctx.Done():
				// 在等待QPS限制时被取消
				task.ResultChan <- &TaskResult{
					Error: fmt.Errorf("queue is shutting down"),
				}
				return
			}

		case <-q.ctx.Done():
			// 上下文已取消，退出处理循环
			if logger.Logger != nil {
				logger.Logger.Info("FaceFusion queue context cancelled, processQueue exiting")
			}
			return

		case <-time.After(1 * time.Second):
			// 定期检查，避免协程阻塞
			continue
		}
	}
}

// handleQueuedTask 处理队列中的单个任务
func (q *QPSLimitedQueue) handleQueuedTask(task *QueuedTask) {
	defer func() {
		if r := recover(); r != nil {
			if logger.Logger != nil {
				logger.Logger.Errorf("FaceFusion task panic recovered: task_id=%s, panic=%v", task.Request.TaskID, r)
			}
			task.ResultChan <- &TaskResult{
				Error: fmt.Errorf("task processing panic: %v", r),
			}
		}
	}()

	q.mutex.Lock()
	q.activeRequests++
	q.mutex.Unlock()

	defer func() {
		q.mutex.Lock()
		q.activeRequests--
		q.totalProcessed++
		q.mutex.Unlock()
	}()

	// 检查任务是否已超时
	if time.Since(task.SubmittedAt) > time.Duration(q.queueTimeoutSeconds)*time.Second {
		task.ResultChan <- &TaskResult{
			Error: fmt.Errorf("task expired in queue: task_id=%s, queued_for=%v",
				task.Request.TaskID, time.Since(task.SubmittedAt)),
		}
		return
	}

	// 处理任务
	var result *bean.FaceFusionTaskResult
	var err error

	if q.processor != nil {
		result, err = q.processor(task.Context, task.Request)
	} else {
		result, err = q.processTaskDirectly(task.Context, task.Request)
	}

	task.ResultChan <- &TaskResult{
		Result: result,
		Error:  err,
	}
}

// processTaskDirectly 直接处理任务（不经过队列）
func (q *QPSLimitedQueue) processTaskDirectly(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
	if logger.Logger != nil {
		logger.Logger.InfofCtx(ctx, "Processing face fusion task directly: task_id=%s", request.TaskID)
	}

	// 实际处理将在task handler中完成，这里只是队列管理
	// 避免循环依赖，不直接调用service层
	return &bean.FaceFusionTaskResult{
		TaskID:      request.TaskID,
		GameID:      request.GameID,
		UserID:      request.UserID,
		ModelID:     request.ModelID,
		ProjectID:   config.GlobConfig.FaceFusion.ProjectID,
		Status:      "queued",
		Message:     "Task queued for processing",
		ProcessedAt: time.Now(),
	}, nil
}

// GetStats 获取队列统计信息
func (q *QPSLimitedQueue) GetStats() *QueueStats {
	q.mutex.RLock()
	defer q.mutex.RUnlock()

	return &QueueStats{
		QueueSize:      len(q.queue),
		ActiveRequests: q.activeRequests,
		TotalProcessed: q.totalProcessed,
		TotalQueued:    q.totalQueued,
		TotalTimeout:   q.totalTimeout,
		TotalRejected:  q.totalRejected,
		QPSLimit:       q.qpsLimit,
		MaxQueueSize:   q.maxQueueSize,
		EnableQPSLimit: q.enableQPSLimit,
	}
}

// Shutdown 关闭队列
func (q *QPSLimitedQueue) Shutdown() {
	q.ShutdownWithTimeout(30 * time.Second)
}

// ShutdownWithTimeout 带超时的关闭队列
func (q *QPSLimitedQueue) ShutdownWithTimeout(timeout time.Duration) {
	if logger.Logger != nil {
		logger.Logger.Info("Shutting down FaceFusion QPS queue...")
	}

	// 保存当前队列中的任务到持久化存储
	q.saveTasksToPersistence()

	// 取消上下文，通知所有goroutine退出
	q.cancel()

	// 关闭队列通道
	close(q.queue)

	// 使用超时等待处理goroutine退出
	done := make(chan struct{})
	go func() {
		q.shutdownWG.Wait()
		close(done)
	}()

	select {
	case <-done:
		if logger.Logger != nil {
			logger.Logger.Info("FaceFusion QPS queue shutdown completed")
		}
	case <-time.After(timeout):
		if logger.Logger != nil {
			logger.Logger.Warn("FaceFusion QPS queue shutdown timeout, some goroutines may still be running")
		}
	}
}

// restoreTasksFromPersistence 从持久化存储恢复任务
func (q *QPSLimitedQueue) restoreTasksFromPersistence() {
	if q.persistence == nil || !q.persistence.IsEnabled() {
		return
	}

	persistentTasks, err := q.persistence.LoadTasks()
	if err != nil {
		if logger.Logger != nil {
			logger.Logger.Errorf("Failed to restore tasks from persistence: %v", err)
		}
		return
	}

	if len(persistentTasks) == 0 {
		return
	}

	// 过滤掉过期的任务
	now := time.Now()
	validTasks := make([]*PersistentTask, 0)
	for _, task := range persistentTasks {
		if now.Sub(task.SubmittedAt) < time.Duration(q.queueTimeoutSeconds)*time.Second {
			validTasks = append(validTasks, task)
		}
	}

	if len(validTasks) == 0 {
		// 所有任务都过期了，清除持久化数据
		_ = q.persistence.ClearTasks()
		return
	}

	// 将有效任务重新加入队列
	restoredCount := 0
	for _, persistentTask := range validTasks {
		queuedTask := &QueuedTask{
			Request:     persistentTask.Request,
			SubmittedAt: persistentTask.SubmittedAt,
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(), // 使用新的上下文
		}

		select {
		case q.queue <- queuedTask:
			q.mutex.Lock()
			q.totalQueued++
			q.mutex.Unlock()
			restoredCount++
		default:
			// 队列已满，停止恢复
			if logger.Logger != nil {
				logger.Logger.Warnf("Queue full during restoration, stopped at task: %s", persistentTask.TaskID)
			}
			goto restoreComplete
		}
	}

restoreComplete:

	if logger.Logger != nil {
		logger.Logger.Infof("Restored %d tasks from persistence", restoredCount)
	}

	// 清除持久化数据，因为任务已经恢复到内存队列
	_ = q.persistence.ClearTasks()
}

// saveTasksToPersistence 将当前队列中的任务保存到持久化存储
func (q *QPSLimitedQueue) saveTasksToPersistence() {
	if q.persistence == nil || !q.persistence.IsEnabled() {
		return
	}

	// 获取队列中的所有任务（非阻塞方式）
	tasks := make([]*QueuedTask, 0, len(q.queue))
	queueLen := len(q.queue)

	// 从队列中取出所有任务
	for i := 0; i < queueLen; i++ {
		select {
		case task := <-q.queue:
			tasks = append(tasks, task)
		default:
			goto saveComplete
		}
	}

saveComplete:

	// 将任务重新放回队列
	for _, task := range tasks {
		select {
		case q.queue <- task:
			// 成功放回
		default:
			// 队列已满，这不应该发生
			if logger.Logger != nil {
				logger.Logger.Errorf("Failed to put task back to queue during persistence: %s", task.Request.TaskID)
			}
		}
	}

	// 保存到持久化存储
	if len(tasks) > 0 {
		if err := q.persistence.SaveTasks(tasks); err != nil {
			if logger.Logger != nil {
				logger.Logger.Errorf("Failed to save tasks to persistence: %v", err)
			}
		}
	}
}
