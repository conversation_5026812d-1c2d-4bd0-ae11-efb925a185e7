package facefusion

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// PersistentTask 持久化任务结构
type PersistentTask struct {
	Request     *bean.FaceFusionTaskRequest `json:"request"`
	SubmittedAt time.Time                   `json:"submitted_at"`
	TaskID      string                      `json:"task_id"`
}

// QueuePersistence 队列持久化管理器
type QueuePersistence struct {
	redisKey string
	enabled  bool
	ttl      time.Duration // Redis 键的过期时间
}

// NewQueuePersistence 创建队列持久化管理器
func NewQueuePersistence(redisKey string, enabled bool) *QueuePersistence {
	if !enabled {
		return &QueuePersistence{enabled: false}
	}

	// 检查 Redis 客户端是否已初始化
	redisClient := redis.Redis()
	if redisClient == nil {
		if logger.Logger != nil {
			logger.Logger.Warn("Redis client not initialized, disabling persistence")
		}
		return &QueuePersistence{enabled: false}
	}

	// 检查 Redis 连接是否可用
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		if logger.Logger != nil {
			logger.Logger.Errorf("Redis connection failed, disabling persistence: %v", err)
		}
		return &QueuePersistence{enabled: false}
	}

	return &QueuePersistence{
		redisKey: redisKey,
		enabled:  true,
		ttl:      24 * time.Hour, // 默认24小时过期
	}
}

// SaveTasks 保存任务到 Redis
func (p *QueuePersistence) SaveTasks(tasks []*QueuedTask) error {
	if !p.enabled {
		return nil
	}

	ctx := context.Background()

	// 转换为持久化格式
	persistentTasks := make([]*PersistentTask, 0, len(tasks))
	for _, task := range tasks {
		persistentTasks = append(persistentTasks, &PersistentTask{
			Request:     task.Request,
			SubmittedAt: task.SubmittedAt,
			TaskID:      task.Request.TaskID,
		})
	}

	// 序列化为JSON
	data, err := json.Marshal(persistentTasks)
	if err != nil {
		return fmt.Errorf("failed to marshal tasks: %w", err)
	}

	// 保存到 Redis
	if err := redis.Set(ctx, p.redisKey, string(data), p.ttl); err != nil {
		return fmt.Errorf("failed to save tasks to Redis: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Infof("Saved %d tasks to Redis persistent storage (key: %s)", len(tasks), p.redisKey)
	}

	return nil
}

// LoadTasks 从 Redis 加载任务
func (p *QueuePersistence) LoadTasks() ([]*PersistentTask, error) {
	if !p.enabled {
		return nil, nil
	}

	ctx := context.Background()

	// 从 Redis 读取数据
	data, err := redis.Get(ctx, p.redisKey)
	if err != nil {
		if err == redis.Nil {
			// 键不存在，返回空列表
			return nil, nil
		}
		return nil, fmt.Errorf("failed to read tasks from Redis: %w", err)
	}

	// 反序列化
	var persistentTasks []*PersistentTask
	if err := json.Unmarshal([]byte(data), &persistentTasks); err != nil {
		return nil, fmt.Errorf("failed to unmarshal tasks: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Infof("Loaded %d tasks from Redis persistent storage (key: %s)", len(persistentTasks), p.redisKey)
	}

	return persistentTasks, nil
}

// ClearTasks 清除 Redis 中的持久化任务
func (p *QueuePersistence) ClearTasks() error {
	if !p.enabled {
		return nil
	}

	ctx := context.Background()

	// 删除 Redis 键
	if err := redis.Redis().Del(ctx, p.redisKey).Err(); err != nil {
		return fmt.Errorf("failed to clear tasks from Redis: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Infof("Cleared persistent task storage from Redis (key: %s)", p.redisKey)
	}

	return nil
}

// IsEnabled 检查持久化是否启用
func (p *QueuePersistence) IsEnabled() bool {
	return p.enabled
}

// GetRedisKey 获取 Redis 键名
func (p *QueuePersistence) GetRedisKey() string {
	return p.redisKey
}

// SetTTL 设置 Redis 键的过期时间
func (p *QueuePersistence) SetTTL(ttl time.Duration) {
	p.ttl = ttl
}
