package facefusion

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

func TestQueuePersistence(t *testing.T) {
	// 跳过测试如果没有 Redis 连接
	if !isRedisAvailable() {
		t.Skip("Redis not available, skipping persistence tests")
	}

	// 创建持久化管理器
	redisKey := "test:facefusion:queue:persistence"
	persistence := NewQueuePersistence(redisKey, true)
	assert.True(t, persistence.IsEnabled())
	assert.Equal(t, redisKey, persistence.GetRedisKey())

	// 确保测试开始前清理
	_ = persistence.ClearTasks()

	// 创建测试任务
	tasks := []*QueuedTask{
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID:  "task-1",
				GameID:  "game-1",
				UserID:  "user-1",
				ModelID: "model-1",
			},
			SubmittedAt: time.Now(),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID:  "task-2",
				GameID:  "game-2",
				UserID:  "user-2",
				ModelID: "model-2",
			},
			SubmittedAt: time.Now().Add(-1 * time.Minute),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
	}

	// 保存任务
	err := persistence.SaveTasks(tasks)
	assert.NoError(t, err)

	// 验证 Redis 中存在数据
	ctx := context.Background()
	exists, err := redis.Exists(ctx, redisKey)
	assert.NoError(t, err)
	assert.True(t, exists)

	// 加载任务
	loadedTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 2)

	// 验证任务内容
	assert.Equal(t, "task-1", loadedTasks[0].TaskID)
	assert.Equal(t, "game-1", loadedTasks[0].Request.GameID)
	assert.Equal(t, "task-2", loadedTasks[1].TaskID)
	assert.Equal(t, "game-2", loadedTasks[1].Request.GameID)

	// 清除任务
	err = persistence.ClearTasks()
	assert.NoError(t, err)

	// 验证 Redis 中数据已删除
	exists, err = redis.Exists(ctx, redisKey)
	assert.NoError(t, err)
	assert.False(t, exists)

	// 再次加载应该返回空列表
	loadedTasks, err = persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 0)
}

func TestQueuePersistenceDisabled(t *testing.T) {
	// 创建禁用的持久化管理器
	persistence := NewQueuePersistence("", false)
	assert.False(t, persistence.IsEnabled())

	// 创建测试任务
	tasks := []*QueuedTask{
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID: "task-1",
			},
			SubmittedAt: time.Now(),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
	}

	// 保存任务应该成功但不做任何事
	err := persistence.SaveTasks(tasks)
	assert.NoError(t, err)

	// 加载任务应该返回nil
	loadedTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Nil(t, loadedTasks)

	// 清除任务应该成功但不做任何事
	err = persistence.ClearTasks()
	assert.NoError(t, err)
}

func TestQueuePersistenceRedisUnavailable(t *testing.T) {
	// 模拟 Redis 不可用的情况
	// 这个测试需要在没有 Redis 连接的环境中运行
	if isRedisAvailable() {
		t.Skip("Redis is available, skipping Redis unavailable test")
	}

	// 尝试创建持久化管理器
	persistence := NewQueuePersistence("test:key", true)
	assert.False(t, persistence.IsEnabled())
}

func TestQueuePersistenceTTL(t *testing.T) {
	// 跳过测试如果没有 Redis 连接
	if !isRedisAvailable() {
		t.Skip("Redis not available, skipping persistence tests")
	}

	// 创建持久化管理器
	redisKey := "test:facefusion:queue:ttl"
	persistence := NewQueuePersistence(redisKey, true)
	assert.True(t, persistence.IsEnabled())

	// 设置短 TTL
	persistence.SetTTL(1 * time.Second)

	// 确保测试开始前清理
	_ = persistence.ClearTasks()

	// 创建测试任务
	tasks := []*QueuedTask{
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID: "task-ttl",
			},
			SubmittedAt: time.Now(),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
	}

	// 保存任务
	err := persistence.SaveTasks(tasks)
	assert.NoError(t, err)

	// 立即验证数据存在
	loadedTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 1)

	// 等待 TTL 过期
	time.Sleep(2 * time.Second)

	// 验证数据已过期
	loadedTasks, err = persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 0)
}

// isRedisAvailable 检查 Redis 是否可用
func isRedisAvailable() bool {
	redisClient := redis.Redis()
	if redisClient == nil {
		return false
	}
	ctx := context.Background()
	return redisClient.Ping(ctx).Err() == nil
}
