# FaceFusion 队列持久化功能

## 概述

FaceFusion 队列现在支持基于 Redis 的任务持久化功能，确保在应用重启时不会丢失排队中的任务。

## 功能特性

### 1. Redis 持久化
- 使用 Redis 作为持久化存储，替代了之前的文件存储方案
- 支持任务的自动保存和恢复
- 支持 TTL（生存时间）设置，防止过期任务堆积

### 2. 自动任务恢复
- 应用启动时自动从 Redis 恢复未完成的任务
- 过滤掉已过期的任务
- 恢复的任务会重新进入队列等待处理

### 3. 优雅关闭
- 应用关闭时自动保存当前队列中的任务
- 支持超时控制，避免关闭过程阻塞过久

## 配置

持久化功能的启用与 QPS 限制功能绑定：

```yaml
facefusionconf:
  project_id: "your-project-id"
  enable_qps_limit: true  # 启用 QPS 限制时同时启用持久化
  qps_limit: 18
  max_queue_size: 1000
  queue_timeout_seconds: 300
```

### Redis 配置

确保 Redis 配置正确：

```yaml
redisconf:
  type: "single"
  hosts: ["localhost:6379"]
  db: 0
  password: ""
  poolsize: 10
  minidle_cons: 5
```

## 使用方式

### 1. 基本使用

```go
// 获取全局队列实例（自动启用持久化）
queue := GetGlobalQueue()

// 提交任务
ctx := context.Background()
request := &bean.FaceFusionTaskRequest{
    TaskID:  "task-123",
    GameID:  "game-456",
    UserID:  "user-789",
    ModelID: "model-abc",
}

result, err := queue.Submit(ctx, request)
if err != nil {
    log.Printf("Task submission failed: %v", err)
    return
}

log.Printf("Task result: %+v", result)
```

### 2. 手动持久化管理

```go
// 创建持久化管理器
redisKey := "facefusion:queue:my-project"
persistence := NewQueuePersistence(redisKey, true)

// 检查是否启用
if persistence.IsEnabled() {
    log.Println("Persistence is enabled")
}

// 设置 TTL
persistence.SetTTL(24 * time.Hour)

// 手动保存任务
tasks := []*QueuedTask{...}
err := persistence.SaveTasks(tasks)

// 手动加载任务
loadedTasks, err := persistence.LoadTasks()

// 清除持久化数据
err = persistence.ClearTasks()
```

## Redis 键命名规则

持久化使用的 Redis 键格式：
```
facefusion:queue:{project_id}
```

例如：
- `facefusion:queue:my-game-project`
- `facefusion:queue:production-app`

## 错误处理

### 1. Redis 连接失败
如果 Redis 不可用，持久化功能会自动禁用，队列仍然可以正常工作：

```go
// 持久化管理器会自动检测 Redis 状态
persistence := NewQueuePersistence("test-key", true)
if !persistence.IsEnabled() {
    log.Println("Persistence disabled due to Redis unavailability")
}
```

### 2. 任务过期处理
过期的任务在恢复时会被自动过滤：

```go
// 任务过期时间基于队列配置的 queue_timeout_seconds
// 超过此时间的任务在恢复时会被丢弃
```

## 性能考虑

### 1. 内存使用
- 持久化不会显著增加内存使用
- 任务数据以 JSON 格式存储在 Redis 中

### 2. 网络开销
- 保存操作：应用关闭时执行一次
- 加载操作：应用启动时执行一次
- 对正常运行时的性能影响极小

### 3. Redis 存储
- 每个项目使用一个 Redis 键
- 支持 TTL 自动清理过期数据
- JSON 格式存储，便于调试和监控

## 监控和调试

### 1. 日志输出
持久化操作会产生相应的日志：

```
INFO: Saved 5 tasks to Redis persistent storage (key: facefusion:queue:my-project)
INFO: Loaded 3 tasks from Redis persistent storage (key: facefusion:queue:my-project)
INFO: Restored 3 tasks from persistence
WARN: Queue full during restoration, stopped at task: task-123
ERROR: Failed to save tasks to persistence: connection refused
```

### 2. Redis 监控
可以直接查看 Redis 中的持久化数据：

```bash
# 查看所有 facefusion 相关的键
redis-cli KEYS "facefusion:queue:*"

# 查看特定项目的持久化数据
redis-cli GET "facefusion:queue:my-project"

# 查看键的 TTL
redis-cli TTL "facefusion:queue:my-project"
```

## 最佳实践

### 1. 项目 ID 设置
确保每个项目使用唯一的 `project_id`，避免不同项目的任务混淆。

### 2. TTL 设置
根据业务需求设置合适的 TTL：
- 开发环境：较短的 TTL（如 1 小时）
- 生产环境：较长的 TTL（如 24 小时）

### 3. 监控告警
建议监控以下指标：
- Redis 连接状态
- 持久化操作的成功率
- 恢复任务的数量

### 4. 容灾考虑
- Redis 高可用部署
- 定期备份 Redis 数据
- 监控 Redis 存储使用情况
