package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	_tencentCloudHandler *TencentCloudHandler
)

type TencentCloudHandler struct {
	middleware.BaseHandler
	tencentCloudLogic *logic.TencentCloudLogic
}

func SingletonTencentCloudHandler() *TencentCloudHandler {
	if _tencentCloudHandler == nil {
		_tencentCloudHandler = &TencentCloudHandler{
			tencentCloudLogic: logic.SingletonTencentCloudLogic(),
		}
	}
	return _tencentCloudHandler
}

// VerifyRealName 获取实名核身结果
func (h *TencentCloudHandler) VerifyRealName(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.VerifyRealNameReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.tencentCloudLogic.VerifyRealName(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// FaceFusion 人脸融合
func (h *TencentCloudHandler) FaceFusion(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.FaceFusionReq{}
	if !h.Bind(c, req, true) {
		return
	}

	resp, err := h.tencentCloudLogic.FaceFusion(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetFaceFusionImage 获取人脸融合图片（支持加密图片的代理访问）
func (h *TencentCloudHandler) GetFaceFusionImage(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取路径参数
	taskID := c.Param("task_id")
	if taskID == "" {
		h.Fail(c, fmt.Errorf("task_id is required"))
		return
	}

	// 获取查询参数进行权限验证
	gameID := c.Query("game_id")
	userID := c.Query("user_id")

	if gameID == "" || userID == "" {
		h.Fail(c, fmt.Errorf("game_id and user_id are required"))
		return
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 请求获取图片: task_id=%s, game_id=%s, user_id=%s", taskID, gameID, userID)

	// 获取图片服务
	imageService := service.SingletonFaceFusionImageService()

	// 验证访问权限
	if err := imageService.ValidateTaskAccess(ctx, taskID, gameID, userID); err != nil {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 访问权限验证失败: task_id=%s, err=%v", taskID, err)
		h.Fail(c, err)
		return
	}

	// 获取图片数据
	imageData, contentType, err := imageService.GetEncryptedImage(ctx, taskID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 获取图片失败: task_id=%s, err=%v", taskID, err)
		h.Fail(c, err)
		return
	}

	// 如果返回的是URL（未加密的情况），重定向到该URL
	if imageData == nil && contentType != "" {
		logger.Logger.InfofCtx(ctx, "[人脸融合图片] 重定向到公开URL: task_id=%s, url=%s", taskID, contentType)
		c.Redirect(http.StatusFound, contentType)
		return
	}

	// 设置响应头
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", strconv.Itoa(len(imageData)))
	c.Header("Cache-Control", "private, max-age=3600") // 缓存1小时
	c.Header("X-Task-ID", taskID)                      // 添加任务ID到响应头

	// 返回图片数据
	c.Data(http.StatusOK, contentType, imageData)

	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 成功返回图片: task_id=%s, size=%d bytes", taskID, len(imageData))
}
