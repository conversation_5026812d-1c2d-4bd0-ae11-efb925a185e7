package encryption

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"fmt"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// SSECConfig SSE-C加密配置
type SSECConfig struct {
	Algorithm string // 加密算法，通常为"AES256"
	Key       string // 32字节的加密密钥（base64编码）
	KeyMD5    string // 密钥的MD5值（base64编码）
}

// GenerateSSECKey 生成32字节的随机加密密钥
func GenerateSSECKey() (string, error) {
	key := make([]byte, 32) // AES256需要32字节密钥
	_, err := rand.Read(key)
	if err != nil {
		return "", fmt.Errorf("generate random key failed: %w", err)
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// GetSSECConfig 获取SSE-C配置
func GetSSECConfig() (*SSECConfig, error) {
	if !config.GlobConfig.OSS.EnableSSEC {
		return nil, nil // 未启用SSE-C
	}

	key := config.GlobConfig.OSS.SSECEncryptionKey
	if key == "" {
		return nil, fmt.Errorf("SSE-C encryption key not configured")
	}

	// 验证密钥格式
	keyBytes, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return nil, fmt.Errorf("invalid SSE-C key format: %w", err)
	}
	if len(keyBytes) != 32 {
		return nil, fmt.Errorf("SSE-C key must be 32 bytes, got %d bytes", len(keyBytes))
	}

	// 计算密钥的MD5
	keyMD5 := md5.Sum(keyBytes)
	keyMD5Base64 := base64.StdEncoding.EncodeToString(keyMD5[:])

	return &SSECConfig{
		Algorithm: "AES256",
		Key:       key,
		KeyMD5:    keyMD5Base64,
	}, nil
}

// ApplySSECToPutOptions 将SSE-C配置应用到上传选项
func ApplySSECToPutOptions(putOptions *cos.ObjectPutOptions, ssecConfig *SSECConfig) {
	if ssecConfig == nil {
		return
	}

	if putOptions.ObjectPutHeaderOptions == nil {
		putOptions.ObjectPutHeaderOptions = &cos.ObjectPutHeaderOptions{}
	}

	putOptions.ObjectPutHeaderOptions.XCosSSECustomerAglo = ssecConfig.Algorithm
	putOptions.ObjectPutHeaderOptions.XCosSSECustomerKey = ssecConfig.Key
	putOptions.ObjectPutHeaderOptions.XCosSSECustomerKeyMD5 = ssecConfig.KeyMD5
}

// CreateSSECGetOptions 创建带SSE-C配置的下载选项
func CreateSSECGetOptions(ssecConfig *SSECConfig) *cos.ObjectGetOptions {
	if ssecConfig == nil {
		return &cos.ObjectGetOptions{}
	}

	return &cos.ObjectGetOptions{
		XCosSSECustomerAglo:   ssecConfig.Algorithm,
		XCosSSECustomerKey:    ssecConfig.Key,
		XCosSSECustomerKeyMD5: ssecConfig.KeyMD5,
	}
}

// IsFaceFusionEncryptionEnabled 检查人脸融合图片是否启用加密
func IsFaceFusionEncryptionEnabled() bool {
	return config.GlobConfig.OSS.EnableSSEC && config.GlobConfig.OSS.FaceFusionEncryption
}
