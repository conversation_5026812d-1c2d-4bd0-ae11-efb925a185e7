package service

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/encryption"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestSSECKeyGeneration 测试SSE-C密钥生成
func TestSSECKeyGeneration(t *testing.T) {
	key, err := encryption.GenerateSSECKey()
	assert.NoError(t, err)
	assert.NotEmpty(t, key)
	t.Logf("Generated SSE-C key: %s", key)

	// 验证密钥长度（base64编码的32字节应该是44个字符）
	assert.Equal(t, 44, len(key))
}

// TestSSECConfig 测试SSE-C配置
func TestSSECConfig(t *testing.T) {
	// 备份原配置
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalKey := config.GlobConfig.OSS.SSECEncryptionKey
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption

	defer func() {
		// 恢复原配置
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.SSECEncryptionKey = originalKey
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
	}()

	// 测试未启用SSE-C的情况
	config.GlobConfig.OSS.EnableSSEC = false
	ssecConfig, err := encryption.GetSSECConfig()
	assert.NoError(t, err)
	assert.Nil(t, ssecConfig)

	// 测试启用SSE-C但未配置密钥的情况
	config.GlobConfig.OSS.EnableSSEC = true
	config.GlobConfig.OSS.SSECEncryptionKey = ""
	ssecConfig, err = encryption.GetSSECConfig()
	assert.Error(t, err)
	assert.Nil(t, ssecConfig)

	// 测试配置有效密钥的情况
	testKey, err := encryption.GenerateSSECKey()
	assert.NoError(t, err)
	config.GlobConfig.OSS.SSECEncryptionKey = testKey

	ssecConfig, err = encryption.GetSSECConfig()
	assert.NoError(t, err)
	assert.NotNil(t, ssecConfig)
	assert.Equal(t, "AES256", ssecConfig.Algorithm)
	assert.Equal(t, testKey, ssecConfig.Key)
	assert.NotEmpty(t, ssecConfig.KeyMD5)

	t.Logf("SSE-C Config: Algorithm=%s, KeyMD5=%s", ssecConfig.Algorithm, ssecConfig.KeyMD5)
}

// TestFaceFusionEncryptionEnabled 测试人脸融合加密启用检查
func TestFaceFusionEncryptionEnabled(t *testing.T) {
	// 备份原配置
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption

	defer func() {
		// 恢复原配置
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
	}()

	// 测试各种配置组合
	testCases := []struct {
		enableSSEC           bool
		faceFusionEncryption bool
		expected             bool
		description          string
	}{
		{false, false, false, "SSE-C和人脸融合加密都未启用"},
		{true, false, false, "仅启用SSE-C，未启用人脸融合加密"},
		{false, true, false, "仅启用人脸融合加密，未启用SSE-C"},
		{true, true, true, "SSE-C和人脸融合加密都启用"},
	}

	for _, tc := range testCases {
		config.GlobConfig.OSS.EnableSSEC = tc.enableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = tc.faceFusionEncryption

		result := encryption.IsFaceFusionEncryptionEnabled()
		assert.Equal(t, tc.expected, result, tc.description)
	}
}

// TestFaceFusionEncryptedUpload 测试人脸融合加密上传（模拟）
func TestFaceFusionEncryptedUpload(t *testing.T) {
	// 注意：这个测试需要有效的OSS配置才能运行
	// 在CI/CD环境中可能需要跳过
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	uploadService := SingletonUploadService()

	// 备份原配置
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalKey := config.GlobConfig.OSS.SSECEncryptionKey
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption

	defer func() {
		// 恢复原配置
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.SSECEncryptionKey = originalKey
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
	}()

	// 配置加密
	testKey, err := encryption.GenerateSSECKey()
	assert.NoError(t, err)

	config.GlobConfig.OSS.EnableSSEC = true
	config.GlobConfig.OSS.SSECEncryptionKey = testKey
	config.GlobConfig.OSS.FaceFusionEncryption = true

	// 测试base64图片数据（1x1像素的透明PNG）
	testBase64 := "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg=="
	fileName := "test_encrypted_image.png"

	// 尝试上传（如果OSS配置不可用，这里会失败）
	imageURL, err := uploadService.UploadBase64Image(ctx, testBase64, fileName)
	if err != nil {
		t.Logf("Upload failed (expected in test environment): %v", err)
		return
	}

	assert.NotEmpty(t, imageURL)
	assert.Contains(t, imageURL, "face_fusion")
	t.Logf("Uploaded encrypted image URL: %s", imageURL)
}

// TestFaceFusionImageService 测试人脸融合图片服务
func TestFaceFusionImageService(t *testing.T) {
	ctx := context.Background()
	imageService := SingletonFaceFusionImageService()

	// 测试不存在的任务
	imageData, contentType, err := imageService.GetEncryptedImage(ctx, "non-existent-task")
	assert.Error(t, err)
	assert.Nil(t, imageData)
	assert.Empty(t, contentType)

	// 测试权限验证
	err = imageService.ValidateTaskAccess(ctx, "non-existent-task", "test-game", "test-user")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "task not found")
}

// TestFaceFusionEncryptionIntegration 测试人脸融合加密集成
func TestFaceFusionEncryptionIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()
	faceFusionService := SingletonFaceFusionService()

	// 创建测试任务
	taskReq := &bean.FaceFusionTaskRequest{
		GameID:  "test-encryption-game",
		UserID:  "test-encryption-user",
		ModelID: "test-encryption-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/test-face.jpg"},
		},
		TaskID:      "test-encryption-task-001",
		SubmittedAt: time.Now(),
		Priority:    0,
	}

	// 创建记录
	record, err := faceFusionService.CreateFaceFusionRecord(ctx, taskReq)
	if err != nil {
		t.Logf("Create record failed (expected in test environment): %v", err)
		return
	}

	assert.NotNil(t, record)
	assert.Equal(t, "processing", record.Status)
	t.Logf("Created face fusion record with encryption support: ID=%d, TaskID=%s", record.ID, record.TaskID)
}
