package service

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/encryption"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestSecureURLGeneration 测试安全URL生成
func TestSecureURLGeneration(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret
	originalBaseURL := config.GlobConfig.Server.BaseURL
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
		config.GlobConfig.Server.BaseURL = originalBaseURL
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-secure-url"
	config.GlobConfig.Server.BaseURL = "https://test-api.example.com"
	config.GlobConfig.OSS.EnableSSEC = false // 测试未加密情况
	config.GlobConfig.OSS.FaceFusionEncryption = false

	ctx := context.Background()
	imageService := SingletonFaceFusionImageService()

	// 测试未启用加密时的URL生成（应该返回错误，因为没有实际的数据库记录）
	secureURL, err := imageService.GenerateSecureImageURL(ctx, "test-task", "test-game", "test-user", 60)
	assert.Error(t, err) // 期望失败，因为没有数据库记录
	assert.Empty(t, secureURL)

	t.Logf("未启用加密时的测试完成")
}

// TestAccessTokenGeneration 测试访问令牌生成和验证
func TestAccessTokenGeneration(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-token"

	imageService := SingletonFaceFusionImageService()

	// 测试token生成
	taskID := "test-task-123"
	gameID := "test-game-456"
	userID := "test-user-789"
	expireMinutes := 30

	token, err := imageService.generateAccessToken(taskID, gameID, userID, expireMinutes)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)
	t.Logf("生成的token: %s", token)

	// 测试token验证
	parsedTaskID, parsedGameID, parsedUserID, err := imageService.ValidateAccessToken(token)
	assert.NoError(t, err)
	assert.Equal(t, taskID, parsedTaskID)
	assert.Equal(t, gameID, parsedGameID)
	assert.Equal(t, userID, parsedUserID)

	t.Logf("Token验证成功: task_id=%s, game_id=%s, user_id=%s", parsedTaskID, parsedGameID, parsedUserID)
}

// TestAccessTokenExpiration 测试访问令牌过期
func TestAccessTokenExpiration(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-expiration"

	imageService := SingletonFaceFusionImageService()

	// 生成一个立即过期的token（负数分钟）
	taskID := "test-task-expired"
	gameID := "test-game-expired"
	userID := "test-user-expired"
	expireMinutes := -1 // 立即过期

	token, err := imageService.generateAccessToken(taskID, gameID, userID, expireMinutes)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	// 等待一小段时间确保过期
	time.Sleep(100 * time.Millisecond)

	// 验证过期token
	_, _, _, err = imageService.ValidateAccessToken(token)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "token expired")

	t.Logf("过期token正确被拒绝: %v", err)
}

// TestAccessTokenInvalidFormat 测试无效格式的访问令牌
func TestAccessTokenInvalidFormat(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-invalid"

	imageService := SingletonFaceFusionImageService()

	// 测试各种无效格式的token
	invalidTokens := []string{
		"",                         // 空token
		"invalid",                  // 无分隔符
		"invalid.token.format",     // 三个部分
		"aW52YWxpZA==.invalid-sig", // 无效签名
		"invalid-base64.signature", // 无效base64
	}

	for i, token := range invalidTokens {
		_, _, _, err := imageService.ValidateAccessToken(token)
		assert.Error(t, err, "测试用例 %d 应该失败: %s", i+1, token)
		t.Logf("无效token %d 正确被拒绝: %s -> %v", i+1, token, err)
	}
}

// TestSecureURLWithEncryption 测试启用加密时的安全URL生成
func TestSecureURLWithEncryption(t *testing.T) {
	// 备份原配置
	originalJWTSecret := config.GlobConfig.Server.JWTSecret
	originalBaseURL := config.GlobConfig.Server.BaseURL
	originalEnableSSEC := config.GlobConfig.OSS.EnableSSEC
	originalFaceFusionEncryption := config.GlobConfig.OSS.FaceFusionEncryption
	originalSSECKey := config.GlobConfig.OSS.SSECEncryptionKey

	defer func() {
		// 恢复原配置
		config.GlobConfig.Server.JWTSecret = originalJWTSecret
		config.GlobConfig.Server.BaseURL = originalBaseURL
		config.GlobConfig.OSS.EnableSSEC = originalEnableSSEC
		config.GlobConfig.OSS.FaceFusionEncryption = originalFaceFusionEncryption
		config.GlobConfig.OSS.SSECEncryptionKey = originalSSECKey
	}()

	// 设置测试配置
	config.GlobConfig.Server.JWTSecret = "test-jwt-secret-for-encryption"
	config.GlobConfig.Server.BaseURL = "https://test-api-encrypted.example.com"
	config.GlobConfig.OSS.EnableSSEC = true
	config.GlobConfig.OSS.FaceFusionEncryption = true

	// 生成测试密钥
	testKey, err := encryption.GenerateSSECKey()
	assert.NoError(t, err)
	config.GlobConfig.OSS.SSECEncryptionKey = testKey

	// 验证加密功能启用
	enabled := encryption.IsFaceFusionEncryptionEnabled()
	assert.True(t, enabled)

	t.Logf("加密功能测试配置完成，启用状态: %t", enabled)

	// 注意：实际的URL生成需要数据库记录，这里主要测试配置和逻辑
	imageService := SingletonFaceFusionImageService()

	// 测试token生成（不依赖数据库）
	token, err := imageService.generateAccessToken("test-encrypted-task", "test-game", "test-user", 60)
	assert.NoError(t, err)
	assert.NotEmpty(t, token)

	// 验证token
	taskID, gameID, userID, err := imageService.ValidateAccessToken(token)
	assert.NoError(t, err)
	assert.Equal(t, "test-encrypted-task", taskID)
	assert.Equal(t, "test-game", gameID)
	assert.Equal(t, "test-user", userID)

	t.Logf("加密环境下的token生成和验证成功")
}
