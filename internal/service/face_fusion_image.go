package service

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/encryption"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
)

var (
	_faceFusionImageOnce    sync.Once
	_faceFusionImageService *FaceFusionImageService
)

type FaceFusionImageService struct{}

func SingletonFaceFusionImageService() *FaceFusionImageService {
	_faceFusionImageOnce.Do(func() {
		_faceFusionImageService = &FaceFusionImageService{}
	})
	return _faceFusionImageService
}

// GetEncryptedImage 获取加密存储的人脸融合图片
func (s *FaceFusionImageService) GetEncryptedImage(ctx context.Context, taskID string) ([]byte, string, error) {
	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 开始获取加密图片: task_id=%s", taskID)

	// 1. 从数据库获取任务记录
	faceFusionService := SingletonFaceFusionService()
	record, err := faceFusionService.GetFaceFusionByTaskID(ctx, taskID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 查询任务记录失败: task_id=%s, err=%v", taskID, err)
		return nil, "", fmt.Errorf("get face fusion record: %w", err)
	}

	if record == nil {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 任务记录不存在: task_id=%s", taskID)
		return nil, "", fmt.Errorf("task not found: %s", taskID)
	}

	if record.Status != "success" {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 任务未成功完成: task_id=%s, status=%s", taskID, record.Status)
		return nil, "", fmt.Errorf("task not completed successfully: status=%s", record.Status)
	}

	if record.OssURL == "" {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 任务无图片URL: task_id=%s", taskID)
		return nil, "", fmt.Errorf("no image URL found for task: %s", taskID)
	}

	// 2. 检查是否为加密存储
	if !encryption.IsFaceFusionEncryptionEnabled() {
		// 如果未启用加密，直接返回公开URL（向后兼容）
		logger.Logger.InfofCtx(ctx, "[人脸融合图片] 未启用加密，返回公开URL: task_id=%s", taskID)
		return nil, record.ImageURL, nil
	}

	// 3. 解析OSS文件路径
	ossPath, err := s.extractOSSPath(record.OssURL)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 解析OSS路径失败: task_id=%s, oss_url=%s, err=%v", taskID, record.OssURL, err)
		return nil, "", fmt.Errorf("parse OSS path: %w", err)
	}

	// 4. 获取SSE-C配置
	ssecConfig, err := encryption.GetSSECConfig()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 获取SSE-C配置失败: task_id=%s, err=%v", taskID, err)
		return nil, "", fmt.Errorf("get SSE-C config: %w", err)
	}

	// 5. 从COS下载并解密图片
	imageData, contentType, err := s.downloadEncryptedImage(ctx, ossPath, ssecConfig)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合图片] 下载加密图片失败: task_id=%s, oss_path=%s, err=%v", taskID, ossPath, err)
		return nil, "", fmt.Errorf("download encrypted image: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合图片] 成功获取加密图片: task_id=%s, size=%d bytes, content_type=%s", taskID, len(imageData), contentType)
	return imageData, contentType, nil
}

// extractOSSPath 从OSS URL中提取文件路径
func (s *FaceFusionImageService) extractOSSPath(ossURL string) (string, error) {
	// 解析URL
	u, err := url.Parse(ossURL)
	if err != nil {
		return "", fmt.Errorf("parse URL failed: %w", err)
	}

	// 返回路径部分（去掉开头的/）
	path := strings.TrimPrefix(u.Path, "/")
	if path == "" {
		return "", fmt.Errorf("empty path in OSS URL")
	}

	return path, nil
}

// downloadEncryptedImage 从COS下载加密图片
func (s *FaceFusionImageService) downloadEncryptedImage(ctx context.Context, ossPath string, ssecConfig *encryption.SSECConfig) ([]byte, string, error) {
	// 创建COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 创建带SSE-C配置的下载选项
	getOptions := encryption.CreateSSECGetOptions(ssecConfig)

	// 下载文件
	resp, err := client.Object.Get(ctx, ossPath, getOptions)
	if err != nil {
		return nil, "", fmt.Errorf("download from COS failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取文件内容
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read image data failed: %w", err)
	}

	// 获取Content-Type
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		// 根据文件扩展名推断Content-Type
		if strings.HasSuffix(strings.ToLower(ossPath), ".png") {
			contentType = "image/png"
		} else {
			contentType = "image/jpeg" // 默认为JPEG
		}
	}

	return imageData, contentType, nil
}

// ValidateTaskAccess 验证用户是否有权限访问指定任务的图片
func (s *FaceFusionImageService) ValidateTaskAccess(ctx context.Context, taskID, gameID, userID string) error {
	// 从数据库获取任务记录
	faceFusionService := SingletonFaceFusionService()
	record, err := faceFusionService.GetFaceFusionByTaskID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("get face fusion record: %w", err)
	}

	if record == nil {
		return fmt.Errorf("task not found: %s", taskID)
	}

	// 验证游戏ID和用户ID
	if record.GameID != gameID {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 游戏ID不匹配: task_id=%s, expected=%s, actual=%s", taskID, record.GameID, gameID)
		return fmt.Errorf("access denied: game ID mismatch")
	}

	if record.UserID != userID {
		logger.Logger.WarnfCtx(ctx, "[人脸融合图片] 用户ID不匹配: task_id=%s, expected=%s, actual=%s", taskID, record.UserID, userID)
		return fmt.Errorf("access denied: user ID mismatch")
	}

	return nil
}
