package service

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// SetupCOSLifecycleRules 设置COS生命周期规则
func (s *UploadService) SetupCOSLifecycleRules(ctx context.Context) error {
	if config.GlobConfig.OSS.FaceFusionTTLDays <= 0 {
		logger.Logger.InfofCtx(ctx, "[OSS生命周期] TTL配置为0，跳过生命周期规则设置")
		return nil
	}

	// 创建COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 定义生命周期规则
	opt := &cos.BucketPutLifecycleOptions{
		Rules: []cos.BucketLifecycleRule{
			{
				ID:     "face-fusion-auto-delete",
				Status: "Enabled",
				Filter: &cos.BucketLifecycleFilter{
					Prefix: fmt.Sprintf("/%s/face_fusion/", config.GlobConfig.OSS.Env),
				},
				Expiration: &cos.BucketLifecycleExpiration{
					Days: config.GlobConfig.OSS.FaceFusionTTLDays,
				},
			},
		},
	}

	// 应用生命周期规则
	_, err := client.Bucket.PutLifecycle(ctx, opt)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[OSS生命周期] 设置生命周期规则失败: %v", err)
		return fmt.Errorf("failed to set COS lifecycle rules: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[OSS生命周期] 成功设置生命周期规则: %d天后自动删除face_fusion目录下的文件",
		config.GlobConfig.OSS.FaceFusionTTLDays)

	return nil
}

// CheckCOSLifecycleRules 检查COS生命周期规则
func (s *UploadService) CheckCOSLifecycleRules(ctx context.Context) error {
	// 创建COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 获取生命周期规则
	result, _, err := client.Bucket.GetLifecycle(ctx)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 获取生命周期规则失败: %v", err)
		return fmt.Errorf("failed to get COS lifecycle rules: %w", err)
	}

	// 检查是否存在人脸融合相关规则
	faceFusionRuleFound := false
	for _, rule := range result.Rules {
		if rule.ID == "face-fusion-auto-delete" {
			faceFusionRuleFound = true
			logger.Logger.InfofCtx(ctx, "[OSS生命周期] 找到人脸融合自动删除规则: 状态=%s, 前缀=%s, 天数=%d",
				rule.Status, rule.Filter.Prefix, rule.Expiration.Days)
			break
		}
	}

	if !faceFusionRuleFound {
		logger.Logger.WarnfCtx(ctx, "[OSS生命周期] 未找到人脸融合自动删除规则，建议设置生命周期规则")
	}

	return nil
}
