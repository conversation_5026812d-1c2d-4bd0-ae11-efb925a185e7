package api

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

const (
	addr        = ":10000"
	BasePath    = "/admin-console"
	BasePathToC = BasePath + "/api"
)

func StartServerGin() {
	logger.Logger.Infof("server port %s", addr)

	router := generateRouter()
	if err := router.Run(addr); err != nil {
		logger.Logger.Errorf("StartServerGin err: %v", err)
		panic(err)
	}
}

func generateRouter() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(middleware.Cors())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.RequestID())
	router.Use(middleware.CleanPriorityHeader())

	group := router.Group(BasePath)

	// h5打包
	group.POST("/auth/timestamp", handler.SingletonAuthHandler().Timestamp)
	group.POST("/auth/register", handler.SingletonAuthHandler().Register)
	group.POST("/auth/login", handler.SingletonAuthHandler().Login)
	// 实名核身相关接口
	group.POST("/auth/real_name/verify", handler.SingletonTencentCloudHandler().VerifyRealName)
	// 充值限额检查
	group.POST("/auth/check_recharge", handler.SingletonAuthHandler().CheckRecharge)
	// 充值弹窗判断
	group.POST("/auth/check_recharge_popup", handler.SingletonAuthHandler().CheckRechargePopup)

	group.GET("/ping", handler.SingletonUserHandler().Heartbeat)
	// 针对平台 公开 API
	group.GET("/timestamp", handler.SingletonUserHandler().GetTimestamp)

	// 测试接口（无需JWT验证）
	group.POST("/test/product_shipment", handler.SingletonOrderHandler().TestCallbackShipment)

	group.GET("/config/game", handler.SingletonConfigHandler().GetGameConfig)
	group.GET("/config/share", handler.SingletonShareHandler().GetShare)
	group.POST("/config/switches", handler.SingletonConfigHandler().GetSwitches)
	group.POST("/v2/config/switches", middleware.SwitchJWT(), handler.SingletonConfigHandler().GetSwitchesV2)

	group.POST("/login", handler.SingletonUserHandler().Login)
	group.POST("/v2/login", handler.SingletonUserHandler().LoginV2)          // Code加密版本
	group.POST("/v2/test/login", handler.SingletonUserHandler().LoginV2Test) // 测试专用登录接口，用于压力测试
	group.POST("/v2/refresh_login", middleware.DecryptionRefreshToken(), handler.SingletonUserHandler().RefreshLogin)
	group.POST("/v3/refresh_login", middleware.DecryptionRefreshToken(), handler.SingletonUserHandler().RefreshLoginV3) // 刷新登录的加密版本

	group.POST("/subscribe/login", handler.SingletonUserHandler().LoginSubscribe)

	group.POST("/douyin/login", handler.SingletonUserHandler().LoginDouyin)
	group.POST("/v2/douyin/refresh_login", middleware.DecryptionRefreshToken(), handler.SingletonUserHandler().RefreshDouyinLogin)

	// 抖音信息流游戏能力接口
	group.GET("/douyin/feed_game/user_scenes", middleware.DouyinFeedGameSign(), handler.SingletonDouyinFeedGameHandler().QueryUserScenes)

	group.POST("/qq/login", handler.SingletonUserHandler().LoginQQ)
	group.POST("/v2/qq/refresh_login", middleware.DecryptionRefreshToken(), handler.SingletonUserHandler().RefreshQQLogin)
	// SDK调用的数据打点上报
	group.POST("/init_report", handler.SingletonDataReportHandler().InitReport)
	group.POST("/batch_init_report", handler.SingletonDataReportHandler().BatchInitReport)
	group.POST("/upload_report", handler.SingletonDataReportHandler().UploadReport)
	group.GET("/wechat/subscribe/config", handler.SingletonOrderHandler().GetSubscribeConfig)
	// 米大师支付-校验
	group.GET("/wechat/midas_callback/:game_id", handler.SingletonOrderHandler().WechatMidasSignCallback)
	// 米大师支付-回调数据发货通知
	group.POST("/wechat/midas_callback/:game_id", handler.SingletonOrderHandler().WechatMidasCallback)
	group.POST("/wechat/h5/pay_callback", handler.SingletonOrderHandler().WechatH5PayCallback)                  // 微信 IOS
	group.GET("/wechat/customer_callback/:game_id", handler.SingletonOrderHandler().WechatCustomerSignCallback) // iOS 客服回调认证
	group.POST("/wechat/customer_callback/:game_id", handler.SingletonOrderHandler().WechatCustomerCallback)    // iOS 客服回调交互
	// 引力引擎API
	group.GET("/gravity/refresh_wechat_token", handler.SingletonGravityEngineHandler().GetWechatToken) // 引力引擎获取微信token

	group.GET("/douyin/pay_callback/:game_id", handler.SingletonOrderHandler().DouyinPaySignCallback)
	group.POST("/douyin/pay_callback/:game_id", handler.SingletonOrderHandler().DouyinPayCallback)
	// group.POST("/douyin/pay/success", handler.SingletonOrderHandler().DouyinPaySuccess)

	// 抖音客服推送回调
	group.POST("/douyin/customer_callback/:game_id", handler.SingletonCustomerHandler().DouyinCustomerServiceCallback) // 抖音客服回调交互

	// 获取七鱼的code， 模拟登录
	group.POST("/qiyu/auth/code", handler.SingletonQiyuHandler().GetQiyuAuthCode)
	group.POST("/qiyu/auth/login", handler.SingletonQiyuHandler().QiyuAuthLogin)
	group.POST("/qiyu/create_support_ticket", middleware.QiyuJWT(), handler.SingletonQiyuHandler().CreateSupportTicket)
	group.POST("/qiyu/get_ticket_logs", middleware.QiyuJWT(), handler.SingletonQiyuHandler().GetTicketLogs)
	group.POST("/qiyu/get_support_tickets", middleware.QiyuJWT(), handler.SingletonQiyuHandler().GetSupportTickets)
	group.POST("/qiyu/get_ticket_template", middleware.QiyuJWT(), handler.SingletonQiyuHandler().GetTicketTemplate)
	group.POST("/qiyu/get_ticket_detail", middleware.QiyuJWT(), handler.SingletonQiyuHandler().GetTicketDetail)
	group.POST("/qiyu/upload_file", middleware.QiyuJWT(), handler.SingletonQiyuHandler().UploadFile)
	// 回复工单
	group.POST("/qiyu/reply_ticket", middleware.QiyuJWT(), handler.SingletonQiyuHandler().ReplyTicket)

	// 七鱼服务回调
	group.POST("/qiyu/ticket/callback", handler.SingletonQiyuHandler().QiyuTicketCallback)

	// 新版工单系统API（使用同样的中间件，确保权限校验一致）
	group.POST("/workorder/login", handler.SingletonWorkorderHandler().AuthLogin) // 微信小程序授权

	group.POST("/workorder/douyin/sign", handler.SingletonWorkorderHandler().DouyinAuthSign)  // 抖音JS授权
	group.POST("/workorder/douyin/auth", handler.SingletonWorkorderHandler().DouyinAuthLogin) // 抖音JS授权
	group.POST("/workorder/create", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().CreateSupportTicket)
	group.POST("/workorder/detail", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetTicketDetail) // 获取单个工单详情
	group.POST("/workorder/list", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetSupportTickets) // 工单列表
	group.POST("/workorder/upload", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().UploadFile)
	group.POST("/workorder/reply", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().ReplyTicket)
	// 获取当前用户是否有正在进行中的工单，设计的表为m_workorder, 如果存在正在进行中的工单，返回true，并且附带内容和has_new_reply字段
	group.GET("/workorder/ongoing_status", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetOngoingWorkorderStatus)

	// 工单系统 - 客服中心配置
	group.GET("/workorder/customer_service/games", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetGameList)         // 获取客服中心游戏列表
	group.GET("/workorder/customer_service/welcome", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetWelcomeContent) // 获取客服中心欢迎语
	// 获取busy_switch状态
	group.GET("/workorder/customer_service/busy_switch", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().GetBusySwitch) // 获取繁忙提示开关状态
	// 手动触发工单统计邮件发送 (需要认证)
	group.POST("/workorder/stats/trigger_email", middleware.QiyuJWT(), handler.SingletonWorkorderHandler().ManualTriggerWorkorderStats) // 手动触发工单统计邮件
	// 工单系统 - 聊天消息                                                        // 获取聊天消息历史
	group.GET("/workorder/chat/history", middleware.QiyuJWT(), handler.SingletonChatMessageHandler().GetMessageHistory)  // 获取聊天消息历史
	group.POST("/workorder/chat/feedback", middleware.QiyuJWT(), handler.SingletonChatMessageHandler().SaveFeedback)     // 保存聊天反馈
	group.POST("/workorder/chat/message", middleware.QiyuJWT(), handler.SingletonChatMessageHandler().SubmitChatMessage) // 提交聊天消息

	// 针对平台 SDK Token API
	group.Use(middleware.JWT())
	group.GET("/order/detail/h5_pay", handler.SingletonOrderHandler().GetOrderDetailH5Pay) // 获取订单详情，返回签名，此时调用微信支付 API (android 和 iOS 不一致)， 安卓返回签名， iOS 返回 H5签名
	group.GET("/order/detail/h5", handler.SingletonOrderHandler().GetOrderDetailH5)        // 获取H5订单详情
	// 微信小游戏
	group.POST("/refresh_login", handler.SingletonUserHandler().RefreshLogin)
	group.PUT("/user_info", handler.SingletonUserHandler().UpdateUserInfo)
	group.PUT("/v2/user_info", handler.SingletonUserHandler().UpdateUserInfoV2)
	// 抖音小游戏
	group.PUT("/douyin/user_info", handler.SingletonUserHandler().UpdateDouyinUserInfo)
	// QQ小游戏
	group.PUT("/qq/user_info", handler.SingletonUserHandler().UpdateQQUserInfo)
	// 前端调用的数据打点上报
	group.POST("/data_report", handler.SingletonDataReportHandler().DataReport)
	group.POST("/batch_data_report", handler.SingletonDataReportHandler().BatchDataReport)
	// sdk 调用的API，走JWT
	group.GET("/order/detail/signature", handler.SingletonOrderHandler().GetOrderDetailSign)    // 安卓，获取订单详情，返回签名，此时调用微信支付 API (android 和 iOS 不一致)， 安卓返回签名， iOS 返回 H5签名
	group.GET("/order/detail/customer", handler.SingletonOrderHandler().GetOrderDetailCustomer) // 获取订单详情和客服会话参数

	// 创建私密分享
	group.POST("/create/user/activity_id", handler.SingletonShareHandler().CreateUserActivityID)
	// 新增微信小游戏实时语音路由
	group.POST("/voip/join_room", handler.SingletonVoipHandler().JoinRoom)

	// 获取用户支付链接(ios)
	group.POST("/order/pay_url", handler.SingletonOrderHandler().GetOrderPayURL)
	group.POST("/order/pay_qr_code", handler.SingletonOrderHandler().GetOrderPayQRCode)
	// 举报系统 - 数据提交
	group.POST("/report/submit", handler.SingletonReportHandler().SubmitReport)

	// 针对于客户端测试包的模拟下单接口
	group.POST("/order/test", handler.SingletonOrderHandler().TestOrder)
	// 获取验证码-验证码校验
	group.POST("/captcha/config", handler.SingletonCaptchaHandler().GetCaptchaConfig)
	group.POST("/captcha/verify", handler.SingletonCaptchaHandler().VerifyCaptcha)

	group.GET("/config/advertisements", handler.SingletonConfigHandler().GetAd)

	// 游戏玩家数据保存接口
	group.POST("/player/info_douyin", handler.SingletonGamePlayerHandler().SaveDouyinGamePlayer)

	toCGroup := router.Group(BasePathToC)
	toCGroup.Use(middleware.Sign())                                                                                  // 针对产品服务器 Sign API 中间件， Sign
	toCGroup.POST("/order", handler.SingletonOrderHandler().CreateOrder)                                             // 创建订单
	toCGroup.POST("/sensitive/msg", handler.SingletonSensitiveHandler().VerifySensitiveMessage)                      // 敏感词检查
	toCGroup.POST("/wechat_notify/message/subscribe/comb", handler.SingletonMessageHandler().SubMessageNotify)       // 消息订阅
	toCGroup.POST("/douyin_notify/message/subscribe/comb", handler.SingletonMessageHandler().DouyinSubMessageNotify) // 抖音消息订阅
	// 兑换码使用状态回调地址
	toCGroup.POST("/redemption/code", handler.SingletonRedemptionHandler().RedemptionCodeCallback)
	// 只读兑换码， 传递code， 查看兑换码JSON信息
	toCGroup.GET("/redemption/code/info", handler.SingletonRedemptionHandler().GetRedemptionCode)

	toCGroup.POST("/user_storage/set", handler.SingletonStorageHandler().SetUserStorage)
	toCGroup.POST("/user_storage/remove", handler.SingletonStorageHandler().RemoveUserStorage)
	toCGroup.POST("/wechat/user_interactive_storage/set", handler.SingletonStorageHandler().SetWechatUserInteractiveStorage)
	toCGroup.POST("/wechat/user_interactive_storage/decryption", handler.SingletonStorageHandler().GetWechatUserInteractiveStorageDecryption)

	// 解密私密分享
	toCGroup.POST("/decrypt/user/decode_user_private_share", handler.SingletonShareHandler().DecryptUserPrivateShare)
	// 微信客服消息
	toCGroup.POST("/wechat/minigame/push/comb", handler.SingletonCustomerHandler().PushMinigameMessage)

	// 微信last user key
	toCGroup.POST("/wechat/last_user_key", handler.SingletonUserHandler().GetLastUserKey)

	// QR code
	toCGroup.POST("/wechat/qr_code", handler.SingletonQRCodeHandler().CreateQRCode)
	toCGroup.POST("/wechat/qr_code/get", handler.SingletonQRCodeHandler().GetQRCode)
	toCGroup.POST("/wechat/qr_code/get_unlimited", handler.SingletonQRCodeHandler().GetUnlimitedQRCode)

	// 获取RTC room id
	toCGroup.POST("/rtc/create_room_id", handler.SingletonRTCHandler().CreateRoomID)
	toCGroup.POST("/rtc/room_list", handler.SingletonRTCHandler().GetRoomID)

	// 获取微信的日志数据
	toCGroup.GET("/wechat/log", handler.SingletonLogHandler().GetWechatLog)

	// 获取订单总金额
	toCGroup.POST("/order/total", handler.SingletonOrderHandler().GetOrderTotal)
	// 游戏内容监控系统 - 内容上报
	toCGroup.POST("/monitor/report", handler.SingletonContentHandler().ReportContent)
	// 微信数据解密
	toCGroup.POST("/wechat/decrypt", handler.SingletonWechatHandler().DecryptData)

	// 人脸融合
	toCGroup.POST("/face_fusion", handler.SingletonTencentCloudHandler().FaceFusion)
	// 获取人脸融合图片（支持加密图片代理访问）
	toCGroup.GET("/face_fusion/image/:task_id", handler.SingletonTencentCloudHandler().GetFaceFusionImage)
	// 生成人脸融合图片的安全访问URL
	toCGroup.POST("/face_fusion/image/:task_id/url", handler.SingletonTencentCloudHandler().GenerateFaceFusionImageURL)

	return router
}
