# SSE-C加密配置示例
# 将以下配置添加到您的dev.yaml、test.yaml、prod.yaml等配置文件中

OSS:
  env: "your-env"
  domain: "https://your-cdn-domain.com"
  bucket_url: "https://your-bucket.cos.ap-beijing.myqcloud.com"
  secret_id: "your-secret-id"
  secret_key: "your-secret-key"
  face_fusion_ttl_days: 30  # 人脸融合图片生命周期（天）
  
  # SSE-C加密配置（新增）
  enable_sse_c: true  # 启用SSE-C加密
  sse_c_encryption_key: "your-32-byte-base64-encoded-key"  # 32字节密钥的base64编码
  face_fusion_encryption: true  # 人脸融合图片启用加密存储

# 密钥生成示例（请使用您自己生成的密钥）：
# 1. 生成32字节随机密钥：
#    openssl rand -base64 32
# 
# 2. 或者使用Go代码生成：
#    key := make([]byte, 32)
#    rand.Read(key)
#    base64Key := base64.StdEncoding.EncodeToString(key)
#
# 注意：
# - 密钥必须是32字节（256位）
# - 密钥必须使用base64编码
# - 请妥善保管密钥，丢失密钥将无法解密已存储的文件
# - 建议在生产环境中使用环境变量或密钥管理服务来管理密钥
