# 人脸融合SSE-C加密方案实现

## 📋 方案概述

本文档描述了为人脸融合功能实现SSE-C（Server-Side Encryption with Customer-Provided Keys）加密机制的技术方案。该方案确保人脸融合图片在OSS存储中使用客户提供的密钥进行加密，提供更高的数据安全性。

## ✅ 技术可行性确认

### 1. COS SDK支持
- ✅ 当前使用的`cos-go-sdk-v5 v0.7.55`完全支持SSE-C加密
- ✅ 支持上传时加密：`XCosSSECustomerAglo`、`XCosSSECustomerKey`、`XCosSSECustomerKeyMD5`
- ✅ 支持下载时解密：相同的SSE-C参数用于解密

### 2. 架构兼容性
- ✅ 现有OSS上传逻辑易于扩展
- ✅ 数据库schema无需修改
- ✅ 支持向后兼容（未加密的历史数据）

## 🔧 实现方案

### 阶段1：配置和加密工具

#### 1.1 配置扩展
```yaml
OSS:
  # 现有配置...
  # SSE-C加密配置（新增）
  enable_sse_c: true                    # 启用SSE-C加密
  sse_c_encryption_key: "base64-key"    # 32字节密钥的base64编码
  face_fusion_encryption: true          # 人脸融合图片启用加密存储
```

#### 1.2 加密工具类
- `internal/pkg/encryption/sse_c.go`：SSE-C加密配置和工具函数
- 支持密钥生成、配置验证、加密参数应用

### 阶段2：加密上传功能

#### 2.1 上传服务增强
- 修改`UploadBase64Image`方法支持SSE-C加密
- 根据配置自动应用加密参数
- 保持向后兼容性

#### 2.2 加密流程
```go
// 1. 检查是否启用加密
if encryption.IsFaceFusionEncryptionEnabled() {
    // 2. 获取SSE-C配置
    ssecConfig, err := encryption.GetSSECConfig()
    
    // 3. 应用加密参数到上传选项
    encryption.ApplySSECToPutOptions(putOptions, ssecConfig)
}
```

### 阶段3：图片代理访问

#### 3.1 图片获取服务
- `internal/service/face_fusion_image.go`：专门处理加密图片的获取
- 支持权限验证、解密下载、内容代理

#### 3.2 API接口
```
GET /api/face_fusion/image/{task_id}?game_id={game_id}&user_id={user_id}
```

#### 3.3 访问流程
```
1. 验证用户权限（game_id + user_id）
2. 检查任务状态和图片存在性
3. 如果启用加密：从OSS解密下载 → 返回图片数据
4. 如果未加密：重定向到公开URL（向后兼容）
```

## 🔒 安全特性

### 1. 权限控制
- **身份验证**：要求提供game_id和user_id
- **权限验证**：验证用户是否有权访问指定任务的图片
- **任务状态检查**：仅允许访问成功完成的任务图片

### 2. 加密保护
- **客户端密钥**：使用客户提供的32字节AES256密钥
- **密钥管理**：密钥存储在配置中，建议使用环境变量或密钥管理服务
- **传输安全**：图片数据通过HTTPS传输

### 3. 访问控制
- **私有存储**：加密图片在OSS中无法公开访问
- **代理访问**：通过后端服务代理访问，可控制访问权限
- **缓存控制**：设置适当的缓存策略

## 📊 性能考虑

### 1. 代理模式影响
- **延迟增加**：相比直接访问CDN，增加一次后端请求
- **带宽消耗**：图片数据需要通过后端服务传输
- **并发处理**：后端需要处理图片下载和传输的并发请求

### 2. 优化策略
- **缓存机制**：在后端添加图片缓存（Redis/内存）
- **CDN集成**：考虑使用支持动态鉴权的CDN
- **异步处理**：对于大图片，考虑异步处理和进度反馈

### 3. 性能监控
- **响应时间**：监控图片获取接口的响应时间
- **错误率**：监控解密失败和权限验证失败的比例
- **资源使用**：监控内存和带宽使用情况

## 🚀 部署和配置

### 1. 密钥生成
```bash
# 使用OpenSSL生成32字节密钥
openssl rand -base64 32

# 或使用Go代码生成
go run -c "
import (
    \"crypto/rand\"
    \"encoding/base64\"
    \"fmt\"
)
key := make([]byte, 32)
rand.Read(key)
fmt.Println(base64.StdEncoding.EncodeToString(key))
"
```

### 2. 配置部署
```yaml
# 生产环境配置示例
OSS:
  enable_sse_c: true
  sse_c_encryption_key: "${SSE_C_ENCRYPTION_KEY}"  # 从环境变量读取
  face_fusion_encryption: true
```

### 3. 环境变量
```bash
export SSE_C_ENCRYPTION_KEY="your-generated-base64-key"
```

## 🧪 测试验证

### 1. 单元测试
- SSE-C配置验证
- 加密参数应用
- 权限验证逻辑

### 2. 集成测试
- 加密上传流程
- 解密下载流程
- 端到端图片访问

### 3. 性能测试
- 图片获取接口性能
- 并发访问测试
- 大图片处理测试

## 📈 监控和运维

### 1. 关键指标
- 图片获取成功率
- 平均响应时间
- 解密失败率
- 权限验证失败率

### 2. 日志记录
- 图片访问请求日志
- 加密/解密操作日志
- 权限验证失败日志
- 性能指标日志

### 3. 告警配置
- 解密失败率超过阈值
- 响应时间超过阈值
- 权限验证失败率异常

## 🔄 迁移策略

### 1. 渐进式部署
1. **阶段1**：部署加密功能，默认关闭
2. **阶段2**：在测试环境验证功能
3. **阶段3**：在生产环境启用加密
4. **阶段4**：监控和优化性能

### 2. 向后兼容
- 支持访问历史未加密图片
- 新图片使用加密存储
- 前端无需修改（URL格式保持一致）

### 3. 回滚方案
- 可通过配置快速关闭加密功能
- 历史数据访问不受影响
- 监控指标支持快速问题定位

## 📝 总结

SSE-C加密方案为人脸融合功能提供了企业级的数据安全保护，同时保持了良好的向后兼容性和可扩展性。通过合理的架构设计和性能优化，该方案可以在保证安全性的同时维持良好的用户体验。
