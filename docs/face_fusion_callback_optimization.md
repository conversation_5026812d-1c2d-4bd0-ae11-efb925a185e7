# 人脸融合回调优化实现

## 📋 修改概述

根据您的需求，我们已成功修改了人脸融合任务处理流程中的回调逻辑，实现了以下优化：

1. **在回调前生成安全URL**：在发送回调给前端之前，自动生成安全访问URL
2. **8天有效期**：将安全URL的有效期从60分钟延长到8天（11520分钟）
3. **向后兼容**：保持对未启用加密场景的兼容性

## 🔧 **核心修改内容**

### 1. 任务处理流程修改

<augment_code_snippet path="pkg/task/register_func.go" mode="EXCERPT">
```go
// 5. 生成安全访问URL（在发送回调之前）
if result.Status == "success" && result.FusedImage == "" {
    // 只有成功且已上传到OSS的任务才生成安全URL
    imageService := service.SingletonFaceFusionImageService()
    secureURL, urlErr := imageService.GenerateSecureImageURL(ctx, result.TaskID, result.GameID, result.UserID, 11520) // 8天有效期
    if urlErr != nil {
        logger.Logger.WarnfCtx(ctx, "[人脸融合] 生成安全URL失败，使用原URL: task_id=%s, err=%v", result.TaskID, urlErr)
    } else {
        // 将安全URL替换到FusedImage字段用于回调
        result.FusedImage = secureURL
        logger.Logger.InfofCtx(ctx, "[人脸融合] 生成8天有效期安全URL成功: task_id=%s, url=%s", result.TaskID, secureURL)
    }
}

// 6. 发送回调
if callbackErr := sendFaceFusionTaskCallbackNew(ctx, result); callbackErr != nil {
    logger.Logger.ErrorfCtx(ctx, "Failed to send callback: task_id=%s, err=%v", req.TaskID, callbackErr)
}
```
</augment_code_snippet>

### 2. 移除重复的URL生成逻辑

<augment_code_snippet path="internal/service/face_fusion.go" mode="EXCERPT">
```go
// 注意：安全URL的生成现在在任务处理的回调前统一处理
```
</augment_code_snippet>

## 📊 **工作流程对比**

### **优化前的流程**：
```
1. 人脸融合任务完成
2. 上传图片到OSS（如果启用加密则加密上传）
3. 在UpdateFaceFusionResult中生成60分钟有效期的安全URL
4. 将安全URL存储到数据库的image_url字段
5. 发送回调（包含数据库中的URL）
```

### **优化后的流程**：
```
1. 人脸融合任务完成
2. 上传图片到OSS（如果启用加密则加密上传）
3. 更新数据库记录（不生成安全URL）
4. 在发送回调前，动态生成8天有效期的安全URL
5. 将安全URL放入回调数据的FusedImage字段
6. 发送回调（包含8天有效期的安全URL）
```

## 🔒 **安全性和兼容性**

### 1. **加密场景**
- ✅ 启用SSE-C加密时，生成8天有效期的安全令牌URL
- ✅ 令牌包含任务ID、游戏ID、用户ID和过期时间
- ✅ 使用HMAC-SHA256签名防止篡改

### 2. **非加密场景**
- ✅ 未启用加密时，保持原有的公开URL访问方式
- ✅ 向后兼容现有的前端代码

### 3. **失败场景**
- ✅ 任务失败时不生成安全URL
- ✅ 安全URL生成失败时记录警告日志，不影响回调发送

## 📈 **前端体验改善**

### **回调数据示例**

#### **启用加密时**：
```json
{
  "task_id": "facefusion_game123_user456_1234567890",
  "game_id": "game123",
  "user_id": "user456",
  "model_id": "model789",
  "status": "success",
  "message": "Face fusion completed successfully",
  "fused_image": "https://api.example.com/api/face_fusion/image/task123?token=eyJ0YXNrX2lkIjoi...",
  "processed_at": "2024-01-15T10:30:00Z"
}
```

#### **未启用加密时**：
```json
{
  "task_id": "facefusion_game123_user456_1234567890",
  "game_id": "game123",
  "user_id": "user456",
  "model_id": "model789",
  "status": "success",
  "message": "Face fusion completed successfully",
  "fused_image": "https://cdn.example.com/images/face_fusion_result.jpg",
  "processed_at": "2024-01-15T10:30:00Z"
}
```

### **前端使用方式**：
```javascript
// 接收回调后直接使用
const handleFaceFusionCallback = (callbackData) => {
  if (callbackData.status === 'success') {
    // 直接使用fused_image URL，无需额外处理
    displayImage(callbackData.fused_image);
    
    // URL有效期8天，可以保存用于后续访问
    localStorage.setItem(`face_fusion_${callbackData.task_id}`, callbackData.fused_image);
  }
};
```

## 🧪 **测试验证**

所有核心功能已通过测试验证：

1. ✅ **启用加密时的安全URL生成**
2. ✅ **未启用加密时的兼容性**
3. ✅ **失败状态的正确处理**
4. ✅ **8天有效期配置验证**

## ⚙️ **配置要求**

### **必需配置**：
```yaml
Server:
  jwt_secret: "your-jwt-secret-key"
  base_url: "https://your-api-domain.com"

OSS:
  enable_sse_c: true
  sse_c_encryption_key: "${SSE_C_ENCRYPTION_KEY}"
  face_fusion_encryption: true
```

### **环境变量**：
```bash
export SSE_C_ENCRYPTION_KEY="your-32-byte-base64-key"
```

## 🎯 **优化效果**

1. **✅ 更长的有效期**：从60分钟延长到8天，满足长期访问需求
2. **✅ 统一的处理逻辑**：在回调前统一生成安全URL，逻辑更清晰
3. **✅ 更好的用户体验**：前端无需担心URL快速过期
4. **✅ 向后兼容**：不影响现有的未加密场景

## 📝 **总结**

这次优化成功实现了您的需求：

- **回调时机优化**：在发送回调前生成安全URL ✅
- **有效期延长**：从60分钟延长到8天 ✅  
- **向后兼容**：保持对未启用加密场景的支持 ✅

修改后的系统既提供了更好的用户体验，又保持了良好的安全性和兼容性。前端可以直接使用回调中的URL，无需额外的API调用，且URL有效期足够长，满足各种使用场景的需求。
