# 人脸融合OSS上传功能集成说明

## 📋 功能概述

项目已成功集成人脸融合图片OSS上传功能，支持自动上传腾讯云FaceFusion API返回的融合图片到腾讯云COS存储，并支持文件生命周期管理。

## ✅ 已实现功能

### 1. 自动OSS上传

- ✅ 人脸融合成功后自动上传base64图片到OSS
- ✅ 上传完成后将OSS URL存储到数据库
- ✅ 支持MD5去重，避免重复上传相同图片
- ✅ 上传失败时记录详细错误日志

### 2. 文件生命周期管理

- ✅ 支持配置文件TTL（生存时间）
- ✅ 可通过配置`face_fusion_ttl_days`设置过期天数
- ✅ 设置为0表示永不过期

### 3. 错误处理优化

- ✅ OSS上传失败时自动标记任务为失败状态
- ✅ 详细的错误日志记录
- ✅ 内存优化：上传后清空base64数据

## 🔧 配置方法

### 1. 配置文件设置

在您的配置文件（如`dev.yaml`、`prod.yaml`）中添加以下配置：

```yaml
oss:
  env: "production"
  domain: "https://your-cdn-domain.com"
  bucket_url: "https://your-bucket.cos.ap-beijing.myqcloud.com"
  secret_id: "your-secret-id"
  secret_key: "your-secret-key"
  # 人脸融合图片生命周期配置（新增）
  face_fusion_ttl_days: 30  # 30天后自动删除，设置为0表示永不删除
```

### 2. 建议的TTL配置

| 业务场景 | 建议TTL | 说明 |
|---------|---------|------|
| 临时体验 | 7天 | 短期营销活动使用 |
| 常规业务 | 30天 | 平衡存储成本和用户体验 |
| 长期保存 | 90天 | 重要业务数据 |
| 永久保存 | 0 | 关键资料，不自动删除 |

## 📂 文件存储结构

```
OSS Bucket
├── {env}/face_fusion/
│   ├── {timestamp}{random}-face_fusion_{task_id}_{timestamp}.jpg
│   ├── {timestamp}{random}-face_fusion_{task_id}_{timestamp}.jpg
│   └── ...
```

## 🔄 处理流程

```mermaid
graph TD
    A[人脸融合API调用] --> B[腾讯云FaceFusion]
    B --> C{融合成功?}
    C -->|成功| D[获取base64图片]
    C -->|失败| H[记录失败状态]
    D --> E[上传到OSS]
    E --> F{上传成功?}
    F -->|成功| G[更新数据库URL]
    F -->|失败| I[标记任务失败]
    G --> J[发送成功回调]
    I --> K[发送失败回调]
    H --> K
```

## 📊 数据库字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `image_url` | String | 可访问的图片URL（CDN地址） |
| `oss_url` | String | OSS内部URL |
| `status` | String | 处理状态：processing/success/failed |
| `message` | String | 状态消息或错误信息 |

## 🚨 错误处理机制

### 1. OSS上传失败

- 自动将任务状态设置为`failed`
- 错误信息写入`message`字段
- 发送失败回调通知调用方

### 2. 内存优化

- 上传成功后立即清空`result.FusedImage`
- 避免在内存中保留大量base64数据

### 3. 日志记录

```go
// 成功日志
logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传OSS成功: task_id=%s, url=%s", taskID, imageURL)

// 失败日志
logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传图片到OSS失败: task_id=%s, err=%v", taskID, err)
```

## 🧪 测试验证

### 1. 运行测试用例

```bash
# 测试OSS上传功能
go test -v ./internal/service -run TestFaceFusionOSSUploadWithTTL

# 测试结果更新
go test -v ./internal/service -run TestFaceFusionResultUpdate

# 测试错误处理
go test -v ./internal/service -run TestFaceFusionErrorHandling
```

### 2. 监控指标

- OSS上传成功率
- 平均上传时间
- 文件大小分布
- TTL配置效果

## 🔍 故障排查

### 1. 常见问题

**问题：OSS上传失败**

- 检查OSS配置是否正确
- 验证网络连接
- 确认权限设置

**问题：文件未自动删除**

- 检查`face_fusion_ttl_days`配置
- 验证OSS生命周期规则
- 确认腾讯云COS设置

### 2. 日志查看

```bash
# 查看人脸融合相关日志
grep "人脸融合" logs/admin-console-*.log

# 查看OSS上传日志
grep "OSS" logs/admin-console-*.log
```

## 📈 性能优化建议

1. **并发上传**：当前为同步上传，如需优化可考虑异步上传
2. **CDN加速**：配置CDN域名提升图片访问速度
3. **压缩优化**：可在上传前对图片进行适当压缩
4. **缓存策略**：基于MD5的去重机制已实现

## 🔄 升级路径

如需进一步优化，可考虑：

1. 支持多种图片格式
2. 添加图片压缩功能
3. 实现异步上传队列
4. 支持分片上传大文件

---

**版本**：v1.0.0  
**更新时间**：2024年8月15日  
**维护人员**：开发团队
