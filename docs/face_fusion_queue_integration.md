# 人脸融合任务队列整合文档

## 整合概述

本次整合将 `TypeFaceFusion` 任务处理器与 `facefusion` 包的 QPS 限制队列系统进行了整合，实现了对人脸融合任务的 QPS 控制。

## 主要变化

### 1. 队列系统增强 (pkg/facefusion/queue.go)

- **新增 `TaskProcessor` 类型**：定义了任务处理器函数类型
- **新增 `SubmitWithProcessor` 方法**：允许传递自定义处理器函数到队列
- **修改 `handleQueuedTask` 方法**：支持使用自定义处理器或默认处理器
- **修复循环中的无效 break 语句**：使用 goto 替代无效的 break

### 2. 任务处理器整合 (pkg/task/register_func.go)

- **新增 facefusion 包导入**：引入队列系统
- **重构 `handleFaceFusionTaskRequest`**：使用队列进行 QPS 控制
- **新增 `processFaceFusionTaskDirectly`**：提取核心处理逻辑
- **添加降级机制**：队列失败时自动降级到直接处理

## 工作流程

```mermaid
graph TD
    A[请求提交] --> B[asynq 队列]
    B --> C[HandleFaceFusionTask]
    C --> D{QPS限制启用?}
    D -->|是| E[队列 QPS 控制]
    D -->|否| F[直接处理]
    E --> G[实际处理]
    E -->|队列失败| H[降级到直接处理]
    F --> G
    H --> G
    G --> I[处理完成]
```

## 配置说明

队列行为通过配置文件控制：

```yaml
FaceFusion:
  EnableQPSLimit: true/false  # 是否启用 QPS 限制
  QPSLimit: 18               # QPS 限制（默认 18）
  MaxQueueSize: 1000         # 最大队列大小（默认 1000）
  QueueTimeoutSeconds: 300   # 队列超时时间（默认 300 秒）
```

## 性能优势

1. **QPS 控制**：避免对腾讯云 API 的请求过载
2. **队列管理**：合理控制并发处理数量
3. **持久化支持**：支持任务持久化，系统重启后可恢复
4. **降级保护**：队列系统故障时自动降级到直接处理

## 向后兼容性

- ✅ 完全向后兼容现有的 `TypeFaceFusion` 任务
- ✅ 现有的任务提交方式无需修改
- ✅ 配置未启用 QPS 限制时，行为与之前完全一致

## 测试验证

已通过以下测试验证整合正确性：

1. **参数验证测试**：确保请求参数验证正常工作
2. **任务解析测试**：确保 asynq 任务正确解析
3. **队列配置测试**：确保队列配置正确加载
4. **编译测试**：确保整个项目正常编译

## 使用示例

```go
// 正常提交任务（与之前完全一致）
task := asynq.NewTask(TypeFaceFusion, payload)
client.Enqueue(task)

// 查看队列状态
queue := facefusion.GetGlobalQueue()
stats := queue.GetStats()
fmt.Printf("队列状态: %+v", stats)
```

## 注意事项

1. 首次部署时，建议先在测试环境验证配置正确性
2. 生产环境建议逐步启用 QPS 限制，观察系统表现
3. 监控队列统计信息，及时调整配置参数
4. 队列满时会拒绝新任务，需要合理设置队列大小

## 监控指标

队列提供以下统计信息用于监控：

- `QueueSize`: 当前队列大小
- `ActiveRequests`: 当前活跃请求数
- `TotalProcessed`: 总处理数量
- `TotalQueued`: 总排队数量
- `TotalTimeout`: 总超时数量
- `TotalRejected`: 总拒绝数量
